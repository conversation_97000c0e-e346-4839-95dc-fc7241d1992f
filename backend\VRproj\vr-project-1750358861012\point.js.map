{"version": 3, "file": "point.js", "sourceRoot": "", "sources": ["../../../../src/device/configs/hand/point.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAIH,MAAM,CAAC,MAAM,aAAa,GAAa;IACtC,eAAe,EAAE;QAChB,KAAK,EAAE;YACN,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;aACjE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,kBAAkB,EAAE;YACnB,YAAY,EAAE;gBACb,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,wBAAwB,EAAE;YACzB,YAAY,EAAE;gBACb,mBAAmB,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC9D,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,mBAAmB;SAC3B;QACD,sBAAsB,EAAE;YACvB,YAAY,EAAE;gBACb,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,CAAC;gBACjE,mBAAmB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,CAAC;gBACnE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aACjE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,WAAW,EAAE;YACZ,YAAY,EAAE;gBACb,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,CAAC;gBACjE,mBAAmB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,CAAC;gBACnE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;aAC/D;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,yBAAyB,EAAE;YAC1B,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,+BAA+B,EAAE;YAChC,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC7D,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,CAAC;aACrE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,mCAAmC,EAAE;YACpC,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC9D,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,oBAAoB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,CAAC;aACpE;YACD,MAAM,EAAE,mBAAmB;SAC3B;QACD,6BAA6B,EAAE;YAC9B,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,oBAAoB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,oBAAoB,EAAE,mBAAmB,EAAE,CAAC,oBAAoB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,kBAAkB,EAAE;YACnB,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,oBAAoB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,mBAAmB,EAAE,CAAC,oBAAoB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,0BAA0B,EAAE;YAC3B,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,gCAAgC,EAAE;YACjC,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,CAAC;gBAC7D,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,mBAAmB;SAC3B;QACD,oCAAoC,EAAE;YACrC,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC9D,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,CAAC;aACpE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,8BAA8B,EAAE;YAC/B,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC;gBACjE,mBAAmB,EAAE,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE,oBAAoB,EAAE,CAAC;aACrE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,mBAAmB,EAAE;YACpB,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC;gBACjE,mBAAmB,EAAE,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,CAAC;aACpE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,wBAAwB,EAAE;YACzB,YAAY,EAAE;gBACb,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;aAChE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,8BAA8B,EAAE;YAC/B,YAAY,EAAE;gBACb,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,CAAC;gBAC9D,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,CAAC;aACjE;YACD,MAAM,EAAE,mBAAmB;SAC3B;QACD,kCAAkC,EAAE;YACnC,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,CAAC,eAAe,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC7D,oBAAoB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAChE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,4BAA4B,EAAE;YAC7B,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBACjE,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAClE,CAAC,oBAAoB,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,CAAC;aACrE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,iBAAiB,EAAE;YAClB,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBACjE,oBAAoB,EAAE,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAClE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,CAAC;aAChE;YACD,MAAM,EAAE,qBAAqB;SAC7B;QACD,yBAAyB,EAAE;YAC1B,YAAY,EAAE;gBACb,kBAAkB,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBAC9D,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC9D,CAAC,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;aAClE;YACD,MAAM,EAAE,mBAAmB;SAC3B;QACD,+BAA+B,EAAE;YAChC,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC;gBAC/D,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;gBAC/D,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,oBAAoB;SAC5B;QACD,mCAAmC,EAAE;YACpC,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC;gBAChE,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAChE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,qBAAqB;SAC7B;QACD,6BAA6B,EAAE;YAC9B,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC;gBAChE,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAClE,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,qBAAqB;SAC7B;QACD,kBAAkB,EAAE;YACnB,YAAY,EAAE;gBACb,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC;gBAChE,mBAAmB,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC;gBAChE,CAAC,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,EAAE,CAAC;gBAClE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,CAAC;aACnE;YACD,MAAM,EAAE,oBAAoB;SAC5B;KACD;IACD,gBAAgB,EAAE;QACjB,oBAAoB,EAAE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,CAAC;QAClE,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,CAAC;QAChE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,mBAAmB,EAAE,CAAC;QACjE,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,CAAC;KACnE;CACD,CAAC"}