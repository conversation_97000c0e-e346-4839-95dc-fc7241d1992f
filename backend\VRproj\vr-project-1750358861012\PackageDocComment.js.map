{"version": 3, "file": "PackageDocComment.js", "sourceRoot": "", "sources": ["../../src/aedoc/PackageDocComment.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AAEjC,kEAA+D;AAE/D,MAAa,iBAAiB;IAC5B;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC/B,UAAyB,EACzB,SAAoB;QAEpB,oFAAoF;QACpF,mFAAmF;QACnF,6BAA6B;QAC7B,EAAE;QACF,sFAAsF;QACtF,uFAAuF;QACvF,+DAA+D;QAC/D,EAAE;QACF,kGAAkG;QAClG,wFAAwF;QACxF,8FAA8F;QAC9F,eAAe;QACf,IAAI,mBAAmB,GAA6B,SAAS,CAAC,CAAC,eAAe;QAE9E,KAAK,MAAM,YAAY,IAAI,EAAE,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YACxG,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC;gBAC/D,MAAM,WAAW,GAAW,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;gBAE1F,uCAAuC;gBACvC,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;oBACnC,oEAAoE;oBACpE,wDAAwD;oBACxD,IAAI,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;wBAChD,mBAAmB,GAAG,YAAY,CAAC;oBACrC,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,uFAAuF;YACvF,8FAA8F;YAC9F,KAAK,MAAM,SAAS,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,MAAM,GAAsB,EAAE,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAC9F,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,wBAAwB,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBAEzF,KAAK,MAAM,YAAY,IAAI,MAAM,EAAE,CAAC;oBAClC,MAAM,WAAW,GAAW,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;oBAE1F,IAAI,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;wBAChD,SAAS,CAAC,aAAa,CAAC,2BAA2B,CACjD,uCAAkB,CAAC,mBAAmB,EACtC,qFAAqF,EACrF,UAAU,EACV,YAAY,CAAC,GAAG,CACjB,CAAC;wBACF,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF;AAhED,8CAgEC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport * as ts from 'typescript';\nimport type { Collector } from '../collector/Collector';\nimport { ExtractorMessageId } from '../api/ExtractorMessageId';\n\nexport class PackageDocComment {\n  /**\n   * For the given source file, see if it starts with a TSDoc comment containing the `@packageDocumentation` tag.\n   */\n  public static tryFindInSourceFile(\n    sourceFile: ts.SourceFile,\n    collector: Collector\n  ): ts.TextRange | undefined {\n    // The @packageDocumentation comment is special because it is not attached to an AST\n    // definition.  Instead, it is part of the \"trivia\" tokens that the compiler treats\n    // as irrelevant white space.\n    //\n    // WARNING: If the comment doesn't precede an export statement, the compiler will omit\n    // it from the *.d.ts file, and API Extractor won't find it.  If this happens, you need\n    // to rearrange your statements to ensure it is passed through.\n    //\n    // This implementation assumes that the \"@packageDocumentation\" will be in the first TSDoc comment\n    // that appears in the entry point *.d.ts file.  We could possibly look in other places,\n    // but the above warning suggests enforcing a standardized layout.  This design choice is open\n    // to feedback.\n    let packageCommentRange: ts.TextRange | undefined = undefined; // empty string\n\n    for (const commentRange of ts.getLeadingCommentRanges(sourceFile.text, sourceFile.getFullStart()) || []) {\n      if (commentRange.kind === ts.SyntaxKind.MultiLineCommentTrivia) {\n        const commentBody: string = sourceFile.text.substring(commentRange.pos, commentRange.end);\n\n        // Choose the first JSDoc-style comment\n        if (/^\\s*\\/\\*\\*/.test(commentBody)) {\n          // But only if it looks like it's trying to be @packageDocumentation\n          // (The TSDoc parser will validate this more rigorously)\n          if (/\\@packageDocumentation/i.test(commentBody)) {\n            packageCommentRange = commentRange;\n          }\n          break;\n        }\n      }\n    }\n\n    if (!packageCommentRange) {\n      // If we didn't find the @packageDocumentation tag in the expected place, is it in some\n      // wrong place?  This sanity check helps people to figure out why there comment isn't working.\n      for (const statement of sourceFile.statements) {\n        const ranges: ts.CommentRange[] = [];\n        ranges.push(...(ts.getLeadingCommentRanges(sourceFile.text, statement.getFullStart()) || []));\n        ranges.push(...(ts.getTrailingCommentRanges(sourceFile.text, statement.getEnd()) || []));\n\n        for (const commentRange of ranges) {\n          const commentBody: string = sourceFile.text.substring(commentRange.pos, commentRange.end);\n\n          if (/\\@packageDocumentation/i.test(commentBody)) {\n            collector.messageRouter.addAnalyzerIssueForPosition(\n              ExtractorMessageId.MisplacedPackageTag,\n              'The @packageDocumentation comment must appear at the top of entry point *.d.ts file',\n              sourceFile,\n              commentRange.pos\n            );\n            break;\n          }\n        }\n      }\n    }\n\n    return packageCommentRange;\n  }\n}\n"]}