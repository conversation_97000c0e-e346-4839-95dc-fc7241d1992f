export declare type OverloadedParameters<T> = T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
    (...args: infer A16): any;
    (...args: infer A17): any;
    (...args: infer A18): any;
    (...args: infer A19): any;
    (...args: infer A20): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 | A16 | A17 | A18 | A19 | A20 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
    (...args: infer A16): any;
    (...args: infer A17): any;
    (...args: infer A18): any;
    (...args: infer A19): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 | A16 | A17 | A18 | A19 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
    (...args: infer A16): any;
    (...args: infer A17): any;
    (...args: infer A18): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 | A16 | A17 | A18 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
    (...args: infer A16): any;
    (...args: infer A17): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 | A16 | A17 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
    (...args: infer A16): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 | A16 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
    (...args: infer A15): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 | A15 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
    (...args: infer A14): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 | A14 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
    (...args: infer A13): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 | A13 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
    (...args: infer A12): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 | A12 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
    (...args: infer A11): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 | A11 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
    (...args: infer A10): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 | A10 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
    (...args: infer A9): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 | A9 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
    (...args: infer A8): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 | A8 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
    (...args: infer A7): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 | A7 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
    (...args: infer A6): any;
} ? A1 | A2 | A3 | A4 | A5 | A6 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
    (...args: infer A5): any;
} ? A1 | A2 | A3 | A4 | A5 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
    (...args: infer A4): any;
} ? A1 | A2 | A3 | A4 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
    (...args: infer A3): any;
} ? A1 | A2 | A3 : T extends {
    (...args: infer A1): any;
    (...args: infer A2): any;
} ? A1 | A2 : T extends {
    (...args: infer A1): any;
} ? A1 : any;
