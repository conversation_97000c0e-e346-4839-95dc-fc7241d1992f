{"version": 3, "file": "PrimitiveTypes.js", "sourceRoot": "", "sources": ["../src/PrimitiveTypes.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON>ICEN<PERSON> in the project root for license information.\n\n/**\n * A \"branded type\" is a primitive type with a compile-type key that makes it incompatible with other\n * aliases for the primitive type.\n *\n * @remarks\n *\n * Example usage:\n *\n * ```ts\n * // PhoneNumber is a branded type based on the \"string\" primitive.\n * type PhoneNumber = Brand<string, 'PhoneNumber'>;\n *\n * function createPhoneNumber(input: string): PhoneNumber {\n *   if (!/\\d+(\\-\\d+)+/.test(input)) {\n *     throw new Error('Invalid phone number: ' + JSON.stringify(input));\n *   }\n *   return input as <PERSON>Num<PERSON>;\n * }\n *\n * const p1: PhoneNumber = createPhoneNumber('************');\n *\n * // PhoneNumber is a string and can be used as one:\n * const p2: string = p1;\n *\n * // But an arbitrary string cannot be implicitly type cast as PhoneNumber.\n * // ERROR: Type 'string' is not assignable to type 'PhoneNumber'\n * const p3: PhoneNumber = '************';\n * ```\n *\n * For more information about this pattern, see {@link\n * https://github.com/Microsoft/TypeScript/blob/7b48a182c05ea4dea81bab73ecbbe9e013a79e99/src/compiler/types.ts#L693-L698\n * | this comment} explaining the TypeScript compiler's introduction of this pattern, and\n * {@link https://spin.atomicobject.com/2018/01/15/typescript-flexible-nominal-typing/ | this article}\n * explaining the technique in depth.\n *\n * @public\n */\nexport type Brand<T, BrandTag extends string> = T & { __brand: BrandTag };\n"]}