{"version": 3, "file": "PlainTextEmitter.js", "sourceRoot": "", "sources": ["../../src/emitters/PlainTextEmitter.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EACL,OAAO,EACP,WAAW,EAMZ,MAAM,UAAU,CAAC;AAElB;;GAEG;AACH;IAAA;IAkHA,CAAC;IAxFe,kCAAiB,GAA/B,UACE,WAA6C,EAC7C,kBAA2B;QAE3B,IAAI,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,GAAG,CAAC,EAAE,CAAC;YAC/D,kBAAkB,GAAG,CAAC,CAAC,CAAC,UAAU;QACpC,CAAC;QAED,IAAI,KAA6B,CAAC;QAClC,IAAI,WAAW,YAAY,OAAO,EAAE,CAAC;YACnC,KAAK,GAAG,CAAC,WAAW,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,WAAW,CAAC;QACtB,CAAC;QAED,IAAM,eAAe,GAAW,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAEhG,OAAO,eAAe,IAAI,kBAAkB,CAAC;IAC/C,CAAC;IAEc,iCAAgB,GAA/B,UACE,KAA6B,EAC7B,kBAA0B,EAC1B,eAAuB;QAEvB,KAAmB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE,CAAC;YAAtB,IAAM,IAAI,cAAA;YACb,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,WAAW,CAAC,UAAU;oBACzB,IAAM,aAAa,GAAkB,IAAqB,CAAC;oBAC3D,eAAe,IAAI,gBAAgB,CAAC,wBAAwB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBACjF,MAAM;gBAER,KAAK,WAAW,CAAC,QAAQ;oBACvB,IAAM,WAAW,GAAgB,IAAmB,CAAC;oBACrD,eAAe,IAAI,gBAAgB,CAAC,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBAC/E,MAAM;gBACR,KAAK,WAAW,CAAC,WAAW;oBAC1B,IAAM,cAAc,GAAmB,IAAsB,CAAC;oBAC9D,eAAe,IAAI,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;oBACzF,MAAM;gBAER,KAAK,WAAW,CAAC,OAAO;oBACtB,IAAM,UAAU,GAAe,IAAkB,CAAC;oBAClD,eAAe,IAAI,gBAAgB,CAAC,wBAAwB,CAAC,UAAU,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;oBACxF,MAAM;gBAER,KAAK,WAAW,CAAC,SAAS;oBACxB,IAAM,YAAY,GAAiB,IAAoB,CAAC;oBACxD,eAAe,IAAI,gBAAgB,CAAC,wBAAwB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAChF,MAAM;YACV,CAAC;YAED,IAAI,eAAe,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,MAAM;YACR,CAAC;YAED,eAAe,IAAI,gBAAgB,CAAC,gBAAgB,CAClD,IAAI,CAAC,aAAa,EAAE,EACpB,kBAAkB,EAClB,eAAe,CAChB,CAAC;YAEF,IAAI,eAAe,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEc,yCAAwB,GAAvC,UAAwC,CAAS;QAC/C,IAAI,KAAK,GAAW,CAAC,CAAC;QACtB,IAAM,MAAM,GAAW,CAAC,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,GAAW,CAAC,CAAC;QAClB,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC;YAClB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxB,KAAK,EAAE,CAAC,CAAC,QAAQ;gBACjB,KAAK,CAAC,CAAC,CAAC,MAAM;gBACd,KAAK,EAAE,CAAC,CAAC,KAAK;gBACd,KAAK,EAAE,EAAE,KAAK;oBACZ,MAAM;gBACR;oBACE,EAAE,KAAK,CAAC;YACZ,CAAC;YACD,EAAE,CAAC,CAAC;QACN,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACH,uBAAC;AAAD,CAAC,AAlHD,IAkHC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport {\r\n  DocN<PERSON>,\r\n  DocNodeKind,\r\n  type DocPlainText,\r\n  type DocFencedCode,\r\n  type DocCodeSpan,\r\n  type DocLinkTag,\r\n  type DocEscapedText\r\n} from '../nodes';\r\n\r\n/**\r\n * Renders a DocNode tree as plain text, without any rich text formatting or markup.\r\n */\r\nexport class PlainTextEmitter {\r\n  /**\r\n   * Returns true if the specified node contains any text content.\r\n   *\r\n   * @remarks\r\n   * A documentation tool can use this test to report warnings when a developer neglected to write a code comment\r\n   * for a declaration.\r\n   *\r\n   * @param node - this node and all its children will be considered\r\n   * @param requiredCharacters - The test returns true if at least this many non-spacing characters are found.\r\n   * The default value is 1.\r\n   */\r\n  public static hasAnyTextContent(node: DocNode, requiredCharacters?: number): boolean;\r\n\r\n  /**\r\n   * Returns true if the specified collection of nodes contains any text content.\r\n   *\r\n   * @remarks\r\n   * A documentation tool can use this test to report warnings when a developer neglected to write a code comment\r\n   * for a declaration.\r\n   *\r\n   * @param nodes - the collection of nodes to be tested\r\n   * @param requiredCharacters - The test returns true if at least this many non-spacing characters are found.\r\n   * The default value is 1.\r\n   */\r\n  public static hasAnyTextContent(nodes: ReadonlyArray<DocNode>, requiredCharacters?: number): boolean;\r\n  public static hasAnyTextContent(\r\n    nodeOrNodes: DocNode | ReadonlyArray<DocNode>,\r\n    requiredCharacters?: number\r\n  ): boolean {\r\n    if (requiredCharacters === undefined || requiredCharacters < 1) {\r\n      requiredCharacters = 1; // default\r\n    }\r\n\r\n    let nodes: ReadonlyArray<DocNode>;\r\n    if (nodeOrNodes instanceof DocNode) {\r\n      nodes = [nodeOrNodes];\r\n    } else {\r\n      nodes = nodeOrNodes;\r\n    }\r\n\r\n    const foundCharacters: number = PlainTextEmitter._scanTextContent(nodes, requiredCharacters, 0);\r\n\r\n    return foundCharacters >= requiredCharacters;\r\n  }\r\n\r\n  private static _scanTextContent(\r\n    nodes: ReadonlyArray<DocNode>,\r\n    requiredCharacters: number,\r\n    foundCharacters: number\r\n  ): number {\r\n    for (const node of nodes) {\r\n      switch (node.kind) {\r\n        case DocNodeKind.FencedCode:\r\n          const docFencedCode: DocFencedCode = node as DocFencedCode;\r\n          foundCharacters += PlainTextEmitter._countNonSpaceCharacters(docFencedCode.code);\r\n          break;\r\n\r\n        case DocNodeKind.CodeSpan:\r\n          const docCodeSpan: DocCodeSpan = node as DocCodeSpan;\r\n          foundCharacters += PlainTextEmitter._countNonSpaceCharacters(docCodeSpan.code);\r\n          break;\r\n        case DocNodeKind.EscapedText:\r\n          const docEscapedText: DocEscapedText = node as DocEscapedText;\r\n          foundCharacters += PlainTextEmitter._countNonSpaceCharacters(docEscapedText.decodedText);\r\n          break;\r\n\r\n        case DocNodeKind.LinkTag:\r\n          const docLinkTag: DocLinkTag = node as DocLinkTag;\r\n          foundCharacters += PlainTextEmitter._countNonSpaceCharacters(docLinkTag.linkText || '');\r\n          break;\r\n\r\n        case DocNodeKind.PlainText:\r\n          const docPlainText: DocPlainText = node as DocPlainText;\r\n          foundCharacters += PlainTextEmitter._countNonSpaceCharacters(docPlainText.text);\r\n          break;\r\n      }\r\n\r\n      if (foundCharacters >= requiredCharacters) {\r\n        break;\r\n      }\r\n\r\n      foundCharacters += PlainTextEmitter._scanTextContent(\r\n        node.getChildNodes(),\r\n        requiredCharacters,\r\n        foundCharacters\r\n      );\r\n\r\n      if (foundCharacters >= requiredCharacters) {\r\n        break;\r\n      }\r\n    }\r\n\r\n    return foundCharacters;\r\n  }\r\n\r\n  private static _countNonSpaceCharacters(s: string): number {\r\n    let count: number = 0;\r\n    const length: number = s.length;\r\n    let i: number = 0;\r\n    while (i < length) {\r\n      switch (s.charCodeAt(i)) {\r\n        case 32: // space\r\n        case 9: // tab\r\n        case 13: // CR\r\n        case 10: // LF\r\n          break;\r\n        default:\r\n          ++count;\r\n      }\r\n      ++i;\r\n    }\r\n    return count;\r\n  }\r\n}\r\n"]}