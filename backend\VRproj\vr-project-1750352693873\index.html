<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VR Space Shooter Game</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/effects/StereoEffect.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/DeviceOrientationControls.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #gameContainer {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
        }

        #vrButton {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px 25px;
            color: white;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.2s;
        }

        #vrButton:hover {
            transform: scale(1.05);
            background: linear-gradient(45deg, #764ba2 0%, #667eea 100%);
        }

        #stereoButton {
            position: absolute;
            bottom: 20px;
            left: 180px;
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            padding: 12px 25px;
            color: white;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.2s;
        }

        #stereoButton:hover {
            transform: scale(1.05);
            background: linear-gradient(45deg, #ee5a24 0%, #ff6b6b 100%);
        }

        #fullscreenButton {
            position: absolute;
            bottom: 20px;
            left: 360px;
            background: linear-gradient(45deg, #10ac84 0%, #00d2d3 100%);
            border: none;
            padding: 12px 25px;
            color: white;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.2s;
        }

        #fullscreenButton:hover {
            transform: scale(1.05);
            background: linear-gradient(45deg, #00d2d3 0%, #10ac84 100%);
        }

        #instructions {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            text-align: center;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }

        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid white;
            border-radius: 50%;
            z-index: 1000;
            pointer-events: none;
        }

        .crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="ui">
            <div>Score: <span id="score">0</span></div>
            <div>Targets Hit: <span id="targets">0</span></div>
            <div>Health: <span id="health">100</span></div>
        </div>

        <div class="crosshair"></div>

        <div id="instructions">
            🎮 Click to shoot • WASD to move • Mouse to look around<br>
            📱 Try Stereo Mode for VR-like experience!
        </div>

        <button id="vrButton">🥽 VR Mode</button>
        <button id="stereoButton">👀 Stereo View</button>
        <button id="fullscreenButton">🖥️ Fullscreen</button>
    </div>

    <script>
        // Game variables
        let scene, camera, renderer, controls;
        let targets = [];
        let bullets = [];
        let stars = [];
        let score = 0;
        let targetsHit = 0;
        let health = 100;
        let gameRunning = true;
        let stereoEffect = null;
        let isStereoMode = false;
        let isFullscreen = false;
        let isVRMode = false;

        // Initialize the game
        function init() {
            // Create scene
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x000000, 1, 1000);

            // Create camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 5);

            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000011);
            document.getElementById('gameContainer').appendChild(renderer.domElement);

            // Initialize stereo effect
            if (typeof THREE.StereoEffect !== 'undefined') {
                stereoEffect = new THREE.StereoEffect(renderer);
                stereoEffect.setSize(window.innerWidth, window.innerHeight);
            }

            // Add controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.enablePan = false; // More VR-like
            controls.enableZoom = false; // More VR-like

            // Create starfield
            createStarfield();

            // Create initial targets
            createTargets();

            // Add event listeners
            document.addEventListener('click', onMouseClick);
            document.addEventListener('keydown', onKeyDown);
            window.addEventListener('resize', onWindowResize);

            // Start game loop
            animate();
        }

        function createStarfield() {
            const starGeometry = new THREE.BufferGeometry();
            const starCount = 1000;
            const positions = new Float32Array(starCount * 3);

            for (let i = 0; i < starCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 2000;
                positions[i + 1] = (Math.random() - 0.5) * 2000;
                positions[i + 2] = (Math.random() - 0.5) * 2000;
            }

            starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

            const starMaterial = new THREE.PointsMaterial({
                color: 0xffffff,
                size: 2,
                sizeAttenuation: false
            });

            const starField = new THREE.Points(starGeometry, starMaterial);
            scene.add(starField);
        }

        function createTargets() {
            for (let i = 0; i < 10; i++) {
                const geometry = new THREE.SphereGeometry(0.5, 8, 6);
                const material = new THREE.MeshBasicMaterial({
                    color: Math.random() * 0xffffff,
                    wireframe: true
                });
                const target = new THREE.Mesh(geometry, material);

                target.position.set(
                    (Math.random() - 0.5) * 50,
                    (Math.random() - 0.5) * 50,
                    (Math.random() - 0.5) * 50 - 20
                );

                target.userData = {
                    velocity: new THREE.Vector3(
                        (Math.random() - 0.5) * 0.1,
                        (Math.random() - 0.5) * 0.1,
                        (Math.random() - 0.5) * 0.1
                    ),
                    originalColor: target.material.color.getHex()
                };

                scene.add(target);
                targets.push(target);
            }
        }

        function onMouseClick(event) {
            if (!gameRunning) return;

            // Create bullet
            const bulletGeometry = new THREE.SphereGeometry(0.1, 4, 4);
            const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xffff00 });
            const bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);

            bullet.position.copy(camera.position);

            const direction = new THREE.Vector3();
            camera.getWorldDirection(direction);
            bullet.userData = { velocity: direction.multiplyScalar(2) };

            scene.add(bullet);
            bullets.push(bullet);

            // Check for hits
            checkCollisions();
        }

        function checkCollisions() {
            bullets.forEach((bullet, bulletIndex) => {
                targets.forEach((target, targetIndex) => {
                    if (bullet.position.distanceTo(target.position) < 1) {
                        // Hit!
                        scene.remove(bullet);
                        scene.remove(target);
                        bullets.splice(bulletIndex, 1);
                        targets.splice(targetIndex, 1);

                        // Update score
                        score += 100;
                        targetsHit++;
                        updateUI();

                        // Create new target
                        setTimeout(() => {
                            if (gameRunning) {
                                createSingleTarget();
                            }
                        }, 1000);
                    }
                });
            });
        }

        function createSingleTarget() {
            const geometry = new THREE.SphereGeometry(0.5, 8, 6);
            const material = new THREE.MeshBasicMaterial({
                color: Math.random() * 0xffffff,
                wireframe: true
            });
            const target = new THREE.Mesh(geometry, material);

            target.position.set(
                (Math.random() - 0.5) * 50,
                (Math.random() - 0.5) * 50,
                (Math.random() - 0.5) * 50 - 20
            );

            target.userData = {
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1,
                    (Math.random() - 0.5) * 0.1
                ),
                originalColor: target.material.color.getHex()
            };

            scene.add(target);
            targets.push(target);
        }

        function onKeyDown(event) {
            const speed = 0.5;
            switch(event.code) {
                case 'KeyW':
                    camera.position.z -= speed;
                    break;
                case 'KeyS':
                    camera.position.z += speed;
                    break;
                case 'KeyA':
                    camera.position.x -= speed;
                    break;
                case 'KeyD':
                    camera.position.x += speed;
                    break;
                case 'Space':
                    event.preventDefault();
                    onMouseClick();
                    break;
            }
        }

        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('targets').textContent = targetsHit;
            document.getElementById('health').textContent = health;
        }

        function animate() {
            if (!gameRunning) return;

            requestAnimationFrame(animate);

            // Update targets
            targets.forEach(target => {
                target.position.add(target.userData.velocity);
                target.rotation.x += 0.01;
                target.rotation.y += 0.01;

                // Bounce off boundaries
                if (Math.abs(target.position.x) > 25) target.userData.velocity.x *= -1;
                if (Math.abs(target.position.y) > 25) target.userData.velocity.y *= -1;
                if (Math.abs(target.position.z) > 25) target.userData.velocity.z *= -1;
            });

            // Update bullets
            bullets.forEach((bullet, index) => {
                bullet.position.add(bullet.userData.velocity);

                // Remove bullets that are too far
                if (bullet.position.length() > 100) {
                    scene.remove(bullet);
                    bullets.splice(index, 1);
                }
            });

            controls.update();

            // Render in stereo or normal mode
            if (isStereoMode && stereoEffect) {
                stereoEffect.render(scene, camera);
            } else {
                renderer.render(scene, camera);
            }
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);

            if (stereoEffect) {
                stereoEffect.setSize(window.innerWidth, window.innerHeight);
            }
        }

        function toggleVRMode() {
            isVRMode = !isVRMode;
            const vrButton = document.getElementById('vrButton');

            if (isVRMode) {
                // Activate VR simulation mode
                vrButton.textContent = '🖥️ Exit VR';
                vrButton.style.background = 'linear-gradient(45deg, #ee5a24 0%, #ff6b6b 100%)';

                // Enable stereo rendering
                isStereoMode = true;

                // Auto-enter fullscreen
                if (!isFullscreen) {
                    toggleFullscreen();
                }

                // VR-optimized controls
                controls.enableRotate = true;
                controls.enablePan = false;
                controls.enableZoom = false;
                controls.rotateSpeed = 0.3;
                controls.autoRotate = false;

                // Hide UI elements for immersion
                document.getElementById('ui').style.opacity = '0.7';
                document.getElementById('stereoButton').style.display = 'none';
                document.getElementById('fullscreenButton').style.display = 'none';

                // Show VR instructions
                document.getElementById('instructions').innerHTML =
                    '🥽 VR Mode Active!<br>📱 Move mouse/head to look • Click to shoot • WASD to move<br>🎯 Immersive 3D Experience';
                document.getElementById('instructions').style.background = 'rgba(0,100,200,0.8)';

                // Adjust camera for VR-like experience
                camera.position.set(0, 1.6, 5); // Eye level height

            } else {
                // Exit VR mode
                vrButton.textContent = '🥽 VR Mode';
                vrButton.style.background = 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)';

                // Disable stereo rendering
                isStereoMode = false;

                // Reset controls
                controls.enableRotate = true;
                controls.enablePan = false;
                controls.enableZoom = false;
                controls.rotateSpeed = 1.0;

                // Show UI elements
                document.getElementById('ui').style.opacity = '1';
                document.getElementById('stereoButton').style.display = 'block';
                document.getElementById('fullscreenButton').style.display = 'block';

                // Show normal instructions
                document.getElementById('instructions').innerHTML =
                    '🎮 Click to shoot • WASD to move • Mouse to look around<br>📱 Try VR Mode for immersive experience!';
                document.getElementById('instructions').style.background = 'rgba(0,0,0,0.8)';

                // Reset camera
                camera.position.set(0, 0, 5);
            }
        }

        function toggleStereoMode() {
            if (isVRMode) return; // Don't allow stereo toggle in VR mode

            isStereoMode = !isStereoMode;
            const button = document.getElementById('stereoButton');

            if (isStereoMode) {
                button.textContent = '👁️ Normal View';
                button.style.background = 'linear-gradient(45deg, #ee5a24 0%, #ff6b6b 100%)';

                // Make camera movement more VR-like
                controls.enableRotate = true;
                controls.rotateSpeed = 0.5;

                // Show VR instructions
                document.getElementById('instructions').innerHTML =
                    '🥽 VR Stereo Mode Active!<br>📱 Hold phone horizontally • Move head to look around';
            } else {
                button.textContent = '👀 Stereo View';
                button.style.background = 'linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%)';

                // Reset to normal controls
                controls.enableRotate = true;
                controls.rotateSpeed = 1.0;

                // Show normal instructions
                document.getElementById('instructions').innerHTML =
                    '🎮 Click to shoot • WASD to move • Mouse to look around<br>📱 Try VR Mode for immersive experience!';
            }
        }

        function toggleFullscreen() {
            if (!isFullscreen) {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
                isFullscreen = true;
                document.getElementById('fullscreenButton').textContent = '🪟 Exit Fullscreen';
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                isFullscreen = false;
                document.getElementById('fullscreenButton').textContent = '🖥️ Fullscreen';
            }
        }

        // Button functionality
        document.getElementById('vrButton').addEventListener('click', toggleVRMode);

        document.getElementById('stereoButton').addEventListener('click', toggleStereoMode);
        document.getElementById('fullscreenButton').addEventListener('click', toggleFullscreen);

        // Handle fullscreen change events
        document.addEventListener('fullscreenchange', () => {
            isFullscreen = !!document.fullscreenElement;
            document.getElementById('fullscreenButton').textContent =
                isFullscreen ? '🪟 Exit Fullscreen' : '🖥️ Fullscreen';
        });

        // Start the game
        init();
    </script>
</body>
</html>
