{"version": 3, "file": "PrintUtilities.js", "sourceRoot": "", "sources": ["../src/PrintUtilities.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAoD;AAGpD;;;;GAIG;AACU,QAAA,qBAAqB,GAAW,EAAE,CAAC;AAEhD;;;;GAIG;AACH,MAAa,cAAc;IACzB;;OAEG;IACI,MAAM,CAAC,eAAe;;QAC3B,OAAO,MAAA,OAAO,CAAC,MAAM,0CAAE,OAAO,CAAC;IACjC,CAAC;IA2BM,MAAM,CAAC,SAAS,CACrB,IAAY,EACZ,aAAsB,EACtB,kBAAoC;QAEpC,MAAM,YAAY,GAAa,cAAc,CAAC,gBAAgB,CAC5D,IAAI,EACJ,aAAa,EACb,kBAAwC,CAAC,kCAAkC;SAC5E,CAAC;QACF,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IA+BM,MAAM,CAAC,gBAAgB,CAC5B,IAAY,EACZ,aAAsB,EACtB,kBAAoC;;QAEpC,IAAI,UAAkB,CAAC;QACvB,QAAQ,OAAO,kBAAkB,EAAE,CAAC;YAClC,KAAK,QAAQ;gBACX,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,QAAQ;gBACX,UAAU,GAAG,kBAAkB,CAAC;gBAChC,MAAM;YACR;gBACE,UAAU,GAAG,EAAE,CAAC;gBAChB,MAAM;QACV,CAAC;QAED,MAAM,gBAAgB,GAAW,UAAU,CAAC,MAAM,CAAC;QAEnD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,cAAc,CAAC,eAAe,EAAE,IAAI,6BAAqB,CAAC;QAC5E,CAAC;QAED,4FAA4F;QAC5F,+DAA+D;QAC/D,MAAM,KAAK,GAAa,wBAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,MAAM,GAAG,gBAAgB,IAAI,aAAa,EAAE,CAAC;gBACpD,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,MAAM,oBAAoB,GAAW,CAAA,MAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;gBACnE,MAAM,gBAAgB,GAAW,MAAM,CAAC;gBACxC,IAAI,sBAAsB,GAA2B,IAAI,CAAC;gBAC1D,IAAI,uBAAoD,CAAC;gBACzD,IAAI,qBAAqB,GAAW,oBAAoB,CAAC,MAAM,CAAC;gBAChE,IAAI,oBAAoB,GAAY,KAAK,CAAC;gBAC1C,OAAO,CAAC,sBAAsB,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACvE,IAAI,sBAAsB,CAAC,KAAK,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,aAAa,EAAE,CAAC;wBAC5F,IAAI,mBAAgD,CAAC;wBACrD,IACE,CAAC,uBAAuB;4BACxB,mFAAmF;4BACnF,oBAAoB,EACpB,CAAC;4BACD,mBAAmB,GAAG,sBAAsB,CAAC;wBAC/C,CAAC;6BAAM,CAAC;4BACN,mBAAmB,GAAG,uBAAuB,CAAC;wBAChD,CAAC;wBAED,YAAY,CAAC,IAAI,CACf,UAAU;4BACR,oBAAoB;4BACpB,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,KAAK,CAAC,CACnE,CAAC;wBACF,oBAAoB,GAAG,mBAAmB,CAAC,KAAK,GAAG,qBAAqB,GAAG,aAAa,CAAC;wBACzF,qBAAqB,GAAG,mBAAmB,CAAC,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBACpF,CAAC;yBAAM,CAAC;wBACN,oBAAoB,GAAG,KAAK,CAAC;oBAC/B,CAAC;oBAED,uBAAuB,GAAG,sBAAsB,CAAC;gBACnD,CAAC;gBAED,IACE,uBAAuB;oBACvB,IAAI,CAAC,MAAM,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,aAAa,EACtE,CAAC;oBACD,MAAM,mBAAmB,GAAoB,uBAAuB,CAAC;oBAErE,YAAY,CAAC,IAAI,CACf,UAAU;wBACR,oBAAoB;wBACpB,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,KAAK,CAAC,CACnE,CAAC;oBACF,qBAAqB,GAAG,mBAAmB,CAAC,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACpF,CAAC;gBAED,IAAI,qBAAqB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBACxC,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC;gBAC/F,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,iBAAiB,CAAC,OAAe,EAAE,QAAmB,EAAE,QAAiB;QACrF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,YAAY,GAAW,cAAc,CAAC,eAAe,EAAE,IAAI,6BAAqB,CAAC;YACvF,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,aAAa,GAAW,QAAQ,GAAG,EAAE,CAAC;QAC5C,MAAM,mBAAmB,GAAa,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC9F,IAAI,iBAAiB,GAAW,CAAC,CAAC;QAClC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;YACvC,MAAM,WAAW,GAAW,IAAI,CAAC,IAAI,EAAE,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC/B,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,iBAAiB,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC;YACrC,qFAAqF;YACrF,gBAAgB;YAChB,WAAW;YACX,gBAAgB;YAChB,MAAM,eAAe,GAAW,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACpC,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE,CAAC;gBACvC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YACjC,CAAC;YAED,QAAQ,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,MAAM,OAAO,GAAW,QAAQ,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC1D,MAAM,WAAW,GAAW,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;gBACpD,MAAM,YAAY,GAAW,OAAO,GAAG,WAAW,CAAC;gBACnD,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC/F,CAAC;YACD,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;CACF;AArND,wCAqNC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\nimport { Text } from '@rushstack/node-core-library';\nimport type { ITerminal } from './ITerminal';\n\n/**\n * A sensible fallback column width for consoles.\n *\n * @public\n */\nexport const DEFAULT_CONSOLE_WIDTH: number = 80;\n\n/**\n * A collection of utilities for printing messages to the console.\n *\n * @public\n */\nexport class PrintUtilities {\n  /**\n   * Returns the width of the console, measured in columns\n   */\n  public static getConsoleWidth(): number | undefined {\n    return process.stdout?.columns;\n  }\n\n  /**\n   * Applies word wrapping.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param indent - The number of spaces to indent the wrapped lines, defaults to 0\n   */\n  public static wrapWords(text: string, maxLineLength?: number, indent?: number): string;\n  /**\n   * Applies word wrapping.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param linePrefix - The string to prefix each line with, defaults to ''\n   */\n  public static wrapWords(text: string, maxLineLength?: number, linePrefix?: string): string;\n  /**\n   * Applies word wrapping.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param indentOrLinePrefix - The number of spaces to indent the wrapped lines or the string to prefix\n   * each line with, defaults to no prefix\n   */\n  public static wrapWords(text: string, maxLineLength?: number, indentOrLinePrefix?: number | string): string;\n  public static wrapWords(\n    text: string,\n    maxLineLength?: number,\n    indentOrLinePrefix?: number | string\n  ): string {\n    const wrappedLines: string[] = PrintUtilities.wrapWordsToLines(\n      text,\n      maxLineLength,\n      indentOrLinePrefix as string | undefined // TS is confused by the overloads\n    );\n    return wrappedLines.join('\\n');\n  }\n\n  /**\n   * Applies word wrapping and returns an array of lines.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param indent - The number of spaces to indent the wrapped lines, defaults to 0\n   */\n  public static wrapWordsToLines(text: string, maxLineLength?: number, indent?: number): string[];\n  /**\n   * Applies word wrapping and returns an array of lines.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param linePrefix - The string to prefix each line with, defaults to ''\n   */\n  public static wrapWordsToLines(text: string, maxLineLength?: number, linePrefix?: string): string[];\n  /**\n   * Applies word wrapping and returns an array of lines.\n   *\n   * @param text - The text to wrap\n   * @param maxLineLength - The maximum length of a line, defaults to the console width\n   * @param indentOrLinePrefix - The number of spaces to indent the wrapped lines or the string to prefix\n   * each line with, defaults to no prefix\n   */\n  public static wrapWordsToLines(\n    text: string,\n    maxLineLength?: number,\n    indentOrLinePrefix?: number | string\n  ): string[];\n  public static wrapWordsToLines(\n    text: string,\n    maxLineLength?: number,\n    indentOrLinePrefix?: number | string\n  ): string[] {\n    let linePrefix: string;\n    switch (typeof indentOrLinePrefix) {\n      case 'number':\n        linePrefix = ' '.repeat(indentOrLinePrefix);\n        break;\n      case 'string':\n        linePrefix = indentOrLinePrefix;\n        break;\n      default:\n        linePrefix = '';\n        break;\n    }\n\n    const linePrefixLength: number = linePrefix.length;\n\n    if (!maxLineLength) {\n      maxLineLength = PrintUtilities.getConsoleWidth() || DEFAULT_CONSOLE_WIDTH;\n    }\n\n    // Apply word wrapping and the provided line prefix, while also respecting existing newlines\n    // and prefix spaces that may exist in the text string already.\n    const lines: string[] = Text.splitByNewLines(text);\n\n    const wrappedLines: string[] = [];\n    for (const line of lines) {\n      if (line.length + linePrefixLength <= maxLineLength) {\n        wrappedLines.push(linePrefix + line);\n      } else {\n        const lineAdditionalPrefix: string = line.match(/^\\s*/)?.[0] || '';\n        const whitespaceRegexp: RegExp = /\\s+/g;\n        let currentWhitespaceMatch: RegExpExecArray | null = null;\n        let previousWhitespaceMatch: RegExpExecArray | undefined;\n        let currentLineStartIndex: number = lineAdditionalPrefix.length;\n        let previousBreakRanOver: boolean = false;\n        while ((currentWhitespaceMatch = whitespaceRegexp.exec(line)) !== null) {\n          if (currentWhitespaceMatch.index + linePrefixLength - currentLineStartIndex > maxLineLength) {\n            let whitespaceToSplitAt: RegExpExecArray | undefined;\n            if (\n              !previousWhitespaceMatch ||\n              // Handle the case where there are two words longer than the maxLineLength in a row\n              previousBreakRanOver\n            ) {\n              whitespaceToSplitAt = currentWhitespaceMatch;\n            } else {\n              whitespaceToSplitAt = previousWhitespaceMatch;\n            }\n\n            wrappedLines.push(\n              linePrefix +\n                lineAdditionalPrefix +\n                line.substring(currentLineStartIndex, whitespaceToSplitAt.index)\n            );\n            previousBreakRanOver = whitespaceToSplitAt.index - currentLineStartIndex > maxLineLength;\n            currentLineStartIndex = whitespaceToSplitAt.index + whitespaceToSplitAt[0].length;\n          } else {\n            previousBreakRanOver = false;\n          }\n\n          previousWhitespaceMatch = currentWhitespaceMatch;\n        }\n\n        if (\n          previousWhitespaceMatch &&\n          line.length + linePrefixLength - currentLineStartIndex > maxLineLength\n        ) {\n          const whitespaceToSplitAt: RegExpExecArray = previousWhitespaceMatch;\n\n          wrappedLines.push(\n            linePrefix +\n              lineAdditionalPrefix +\n              line.substring(currentLineStartIndex, whitespaceToSplitAt.index)\n          );\n          currentLineStartIndex = whitespaceToSplitAt.index + whitespaceToSplitAt[0].length;\n        }\n\n        if (currentLineStartIndex < line.length) {\n          wrappedLines.push(linePrefix + lineAdditionalPrefix + line.substring(currentLineStartIndex));\n        }\n      }\n    }\n\n    return wrappedLines;\n  }\n\n  /**\n   * Displays a message in the console wrapped in a box UI.\n   *\n   * @param message - The message to display.\n   * @param terminal - The terminal to write the message to.\n   * @param boxWidth - The width of the box, defaults to half of the console width.\n   */\n  public static printMessageInBox(message: string, terminal: ITerminal, boxWidth?: number): void {\n    if (!boxWidth) {\n      const consoleWidth: number = PrintUtilities.getConsoleWidth() || DEFAULT_CONSOLE_WIDTH;\n      boxWidth = Math.floor(consoleWidth / 2);\n    }\n\n    const maxLineLength: number = boxWidth - 10;\n    const wrappedMessageLines: string[] = PrintUtilities.wrapWordsToLines(message, maxLineLength);\n    let longestLineLength: number = 0;\n    const trimmedLines: string[] = [];\n    for (const line of wrappedMessageLines) {\n      const trimmedLine: string = line.trim();\n      trimmedLines.push(trimmedLine);\n      longestLineLength = Math.max(longestLineLength, trimmedLine.length);\n    }\n\n    if (longestLineLength > boxWidth - 2) {\n      // If the longest line is longer than the box, print bars above and below the message\n      // ═════════════\n      //  Message\n      // ═════════════\n      const headerAndFooter: string = ` ${'═'.repeat(boxWidth)}`;\n      terminal.writeLine(headerAndFooter);\n      for (const line of wrappedMessageLines) {\n        terminal.writeLine(` ${line}`);\n      }\n\n      terminal.writeLine(headerAndFooter);\n    } else {\n      // ╔═══════════╗\n      // ║  Message  ║\n      // ╚═══════════╝\n      terminal.writeLine(` ╔${'═'.repeat(boxWidth - 2)}╗`);\n      for (const trimmedLine of trimmedLines) {\n        const padding: number = boxWidth - trimmedLine.length - 2;\n        const leftPadding: number = Math.floor(padding / 2);\n        const rightPadding: number = padding - leftPadding;\n        terminal.writeLine(` ║${' '.repeat(leftPadding)}${trimmedLine}${' '.repeat(rightPadding)}║`);\n      }\n      terminal.writeLine(` ╚${'═'.repeat(boxWidth - 2)}╝`);\n    }\n  }\n}\n"]}