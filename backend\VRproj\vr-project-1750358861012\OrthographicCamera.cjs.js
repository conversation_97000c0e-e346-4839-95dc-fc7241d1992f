"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("@react-three/fiber"),u=require("./Fbo.cjs.js");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three");var c=n(e),o=a(r);const s=o.forwardRef((({envMap:e,resolution:r=256,frames:n=1/0,children:a,makeDefault:s,...i},l)=>{const f=t.useThree((({set:e})=>e)),d=t.useThree((({camera:e})=>e)),h=t.useThree((({size:e})=>e)),b=o.useRef(null);o.useImperativeHandle(l,(()=>b.current),[]);const g=o.useRef(null),m=u.useFBO(r);o.useLayoutEffect((()=>{i.manual||b.current.updateProjectionMatrix()}),[h,i]),o.useLayoutEffect((()=>{b.current.updateProjectionMatrix()})),o.useLayoutEffect((()=>{if(s){const e=d;return f((()=>({camera:b.current}))),()=>f((()=>({camera:e})))}}),[b,s,f]);let p=0,j=null;const v="function"==typeof a;return t.useFrame((r=>{v&&(n===1/0||p<n)&&(g.current.visible=!1,r.gl.setRenderTarget(m),j=r.scene.background,e&&(r.scene.background=e),r.gl.render(r.scene,b.current),r.scene.background=j,r.gl.setRenderTarget(null),g.current.visible=!0,p++)})),o.createElement(o.Fragment,null,o.createElement("orthographicCamera",c.default({left:h.width/-2,right:h.width/2,top:h.height/2,bottom:h.height/-2,ref:b},i),!v&&a),o.createElement("group",{ref:g},v&&a(m.texture)))}));exports.OrthographicCamera=s;
