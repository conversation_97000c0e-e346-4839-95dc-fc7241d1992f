{"version": 3, "file": "Path.js", "sourceRoot": "", "sources": ["../src/Path.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,2CAA6B;AAkE7B;;;;;GAKG;AACH,MAAa,IAAI;IASf;;;;;;;;;OASG;IACI,MAAM,CAAC,OAAO,CAAC,SAAiB,EAAE,gBAAwB;QAC/D,6EAA6E;QAC7E,uEAAuE;QACvE,sFAAsF;QACtF,MAAM,YAAY,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,cAAc,CAAC,SAAiB,EAAE,gBAAwB;QACtE,MAAM,YAAY,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACxE,OAAO,YAAY,KAAK,EAAE,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAAO,CAAC,KAAa,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,eAAe,CAAC,OAAoC;QAChE,sCAAsC;QACtC,MAAM,YAAY,GAAW,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,cAAc,GAAY,YAAY,KAAK,EAAE,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAElG,IAAI,cAAc,EAAE,CAAC;YACnB,qEAAqE;YACrE,MAAM,aAAa,GAAW,IAAI,CAAC,gBAAgB,CACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CACzD,CAAC;YACF,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,OAAO,aAAa,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAK,aAAa,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAW,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACjE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAAC,OAAuC;QACtE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE5E,mFAAmF;QACnF,kBAAkB;QAClB,MAAM,QAAQ,GAAW,UAAU;YACjC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;gBACnB,aAAa,EAAE,YAAY;gBAC3B,UAAU;gBACV,mBAAmB,EAAE,IAAI;aAC1B,CAAC;YACJ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE/B,IAAI,qBAA6B,CAAC;QAClC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,IAAI,IAAI,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC/C,qBAAqB,GAAG,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;gBAC/C,CAAC;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,qBAAqB,GAAG,IAAI,IAAI,EAAE,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACN,qBAAqB,GAAG,EAAE,CAAC;gBAC7B,CAAC;gBAED,MAAM;YACR,CAAC;YAED,KAAK,cAAc,CAAC,CAAC,CAAC;gBACpB,IAAI,IAAI,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBAC/C,qBAAqB,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC;gBAChD,CAAC;qBAAM,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC9B,qBAAqB,GAAG,IAAI,IAAI,GAAG,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,qBAAqB,GAAG,EAAE,CAAC;gBAC7B,CAAC;gBAED,MAAM;YACR,CAAC;YAED,OAAO,CAAC,CAAC,CAAC;gBACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,OAAO,GAAG,QAAQ,GAAG,qBAAqB,MAAM,OAAO,EAAE,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,SAAiB;QAC9C,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,oBAAoB,CAAC,SAAiB;QAClD,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IACD;;OAEG;IACI,MAAM,CAAC,wBAAwB,CAAC,SAAiB;QACtD,OAAO,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,MAAM,CAAC,kBAAkB,CAAC,SAAiB;QAChD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,uBAAuB;QACvB,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;;AA9LH,oBA+LC;AA9LC,qEAAqE;AACrE,mCAAmC;AACpB,uBAAkB,GAAW,YAAY,CAAC;AAEzD,yDAAyD;AACzD,oBAAoB;AACL,4BAAuB,GAAW,0BAA0B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\nimport * as path from 'path';\n\n/**\n * The format that the FileError message should conform to. The supported formats are:\n *  - Unix: `<path>:<line>:<column> - <message>`\n *  - VisualStudio: `<path>(<line>,<column>) - <message>`\n *\n * @public\n */\nexport type FileLocationStyle = 'Unix' | 'VisualStudio';\n\n/**\n * Options for {@link Path.formatFileLocation}.\n * @public\n */\nexport interface IPathFormatFileLocationOptions {\n  /**\n   * The base path to use when converting `pathToFormat` to a relative path. If not specified,\n   * `pathToFormat` will be used as-is.\n   */\n  baseFolder?: string;\n  /**\n   * The path that will be used to specify the file location.\n   */\n  pathToFormat: string;\n  /**\n   * The message related to the file location.\n   */\n  message: string;\n  /**\n   * The style of file location formatting to use.\n   */\n  format: FileLocationStyle;\n  /**\n   * The optional line number. If not specified, the line number will not be included\n   * in the formatted string.\n   */\n  line?: number;\n  /**\n   * The optional column number. If not specified, the column number will not be included\n   * in the formatted string.\n   */\n  column?: number;\n}\n\n/**\n * Options for {@link Path.formatConcisely}.\n * @public\n */\nexport interface IPathFormatConciselyOptions {\n  /**\n   * The path to be converted.\n   */\n  pathToConvert: string;\n\n  /**\n   * The base path to use when converting `pathToConvert` to a relative path.\n   */\n  baseFolder: string;\n\n  /**\n   * If set to true, don't include the leading `./` if the path is under the base folder.\n   */\n  trimLeadingDotSlash?: boolean;\n}\n\n/**\n * Common operations for manipulating file and directory paths.\n * @remarks\n * This API is intended to eventually be a complete replacement for the NodeJS \"path\" API.\n * @public\n */\nexport class Path {\n  // Matches a relative path consisting entirely of periods and slashes\n  // Example: \".\", \"..\", \"../..\", etc\n  private static _relativePathRegex: RegExp = /^[.\\/\\\\]+$/;\n\n  // Matches a relative path segment that traverses upwards\n  // Example: \"a/../b\"\n  private static _upwardPathSegmentRegex: RegExp = /([\\/\\\\]|^)\\.\\.([\\/\\\\]|$)/;\n\n  /**\n   * Returns true if \"childPath\" is located inside the \"parentFolderPath\" folder\n   * or one of its child folders.  Note that \"parentFolderPath\" is not considered to be\n   * under itself.  The \"childPath\" can refer to any type of file system object.\n   *\n   * @remarks\n   * The indicated file/folder objects are not required to actually exist on disk.\n   * For example, \"parentFolderPath\" is interpreted as a folder name even if it refers to a file.\n   * If the paths are relative, they will first be resolved using path.resolve().\n   */\n  public static isUnder(childPath: string, parentFolderPath: string): boolean {\n    // If childPath is under parentPath, then relativePath will be something like\n    // \"../..\" or \"..\\\\..\", which consists entirely of periods and slashes.\n    // (Note that something like \"....t\" is actually a valid filename, but \"....\" is not.)\n    const relativePath: string = path.relative(childPath, parentFolderPath);\n    return Path._relativePathRegex.test(relativePath);\n  }\n\n  /**\n   * Returns true if \"childPath\" is equal to \"parentFolderPath\", or if it is inside that folder\n   * or one of its children.  The \"childPath\" can refer to any type of file system object.\n   *\n   * @remarks\n   * The indicated file/folder objects are not required to actually exist on disk.\n   * For example, \"parentFolderPath\" is interpreted as a folder name even if it refers to a file.\n   * If the paths are relative, they will first be resolved using path.resolve().\n   */\n  public static isUnderOrEqual(childPath: string, parentFolderPath: string): boolean {\n    const relativePath: string = path.relative(childPath, parentFolderPath);\n    return relativePath === '' || Path._relativePathRegex.test(relativePath);\n  }\n\n  /**\n   * Returns true if `path1` and `path2` refer to the same underlying path.\n   *\n   * @remarks\n   *\n   * The comparison is performed using `path.relative()`.\n   */\n  public static isEqual(path1: string, path2: string): boolean {\n    return path.relative(path1, path2) === '';\n  }\n\n  /**\n   * Formats a path to look nice for reporting purposes.\n   * @remarks\n   * If `pathToConvert` is under the `baseFolder`, then it will be converted to a relative with the `./` prefix\n   * unless the {@link IPathFormatConciselyOptions.trimLeadingDotSlash} option is set to `true`.\n   * Otherwise, it will be converted to an absolute path.\n   *\n   * Backslashes will be converted to slashes, unless the path starts with an OS-specific string like `C:\\`.\n   */\n  public static formatConcisely(options: IPathFormatConciselyOptions): string {\n    // Same logic as Path.isUnderOrEqual()\n    const relativePath: string = path.relative(options.pathToConvert, options.baseFolder);\n    const isUnderOrEqual: boolean = relativePath === '' || Path._relativePathRegex.test(relativePath);\n\n    if (isUnderOrEqual) {\n      // Note that isUnderOrEqual()'s relativePath is the reverse direction\n      const convertedPath: string = Path.convertToSlashes(\n        path.relative(options.baseFolder, options.pathToConvert)\n      );\n      if (options.trimLeadingDotSlash) {\n        return convertedPath;\n      } else {\n        return `./${convertedPath}`;\n      }\n    }\n\n    const absolutePath: string = path.resolve(options.pathToConvert);\n    return absolutePath;\n  }\n\n  /**\n   * Formats a file location to look nice for reporting purposes.\n   * @remarks\n   * If `pathToFormat` is under the `baseFolder`, then it will be converted to a relative with the `./` prefix.\n   * Otherwise, it will be converted to an absolute path.\n   *\n   * Backslashes will be converted to slashes, unless the path starts with an OS-specific string like `C:\\`.\n   */\n  public static formatFileLocation(options: IPathFormatFileLocationOptions): string {\n    const { message, format, pathToFormat, baseFolder, line, column } = options;\n\n    // Convert the path to be relative to the base folder, if specified. Otherwise, use\n    // the path as-is.\n    const filePath: string = baseFolder\n      ? Path.formatConcisely({\n          pathToConvert: pathToFormat,\n          baseFolder,\n          trimLeadingDotSlash: true\n        })\n      : path.resolve(pathToFormat);\n\n    let formattedFileLocation: string;\n    switch (format) {\n      case 'Unix': {\n        if (line !== undefined && column !== undefined) {\n          formattedFileLocation = `:${line}:${column}`;\n        } else if (line !== undefined) {\n          formattedFileLocation = `:${line}`;\n        } else {\n          formattedFileLocation = '';\n        }\n\n        break;\n      }\n\n      case 'VisualStudio': {\n        if (line !== undefined && column !== undefined) {\n          formattedFileLocation = `(${line},${column})`;\n        } else if (line !== undefined) {\n          formattedFileLocation = `(${line})`;\n        } else {\n          formattedFileLocation = '';\n        }\n\n        break;\n      }\n\n      default: {\n        throw new Error(`Unknown format: ${format}`);\n      }\n    }\n\n    return `${filePath}${formattedFileLocation} - ${message}`;\n  }\n\n  /**\n   * Replaces Windows-style backslashes with POSIX-style slashes.\n   *\n   * @remarks\n   * POSIX is a registered trademark of the Institute of Electrical and Electronic Engineers, Inc.\n   */\n  public static convertToSlashes(inputPath: string): string {\n    return inputPath.replace(/\\\\/g, '/');\n  }\n\n  /**\n   * Replaces POSIX-style slashes with Windows-style backslashes\n   *\n   * @remarks\n   * POSIX is a registered trademark of the Institute of Electrical and Electronic Engineers, Inc.\n   */\n  public static convertToBackslashes(inputPath: string): string {\n    return inputPath.replace(/\\//g, '\\\\');\n  }\n  /**\n   * Replaces slashes or backslashes with the appropriate slash for the current operating system.\n   */\n  public static convertToPlatformDefault(inputPath: string): string {\n    return path.sep === '/' ? Path.convertToSlashes(inputPath) : Path.convertToBackslashes(inputPath);\n  }\n\n  /**\n   * Returns true if the specified path is a relative path and does not use `..` to walk upwards.\n   *\n   * @example\n   * ```ts\n   * // These evaluate to true\n   * isDownwardRelative('folder');\n   * isDownwardRelative('file');\n   * isDownwardRelative('folder/');\n   * isDownwardRelative('./folder/');\n   * isDownwardRelative('./folder/file');\n   *\n   * // These evaluate to false\n   * isDownwardRelative('../folder');\n   * isDownwardRelative('folder/../file');\n   * isDownwardRelative('/folder/file');\n   * ```\n   */\n  public static isDownwardRelative(inputPath: string): boolean {\n    if (path.isAbsolute(inputPath)) {\n      return false;\n    }\n    // Does it contain \"..\"\n    if (Path._upwardPathSegmentRegex.test(inputPath)) {\n      return false;\n    }\n    return true;\n  }\n}\n"]}