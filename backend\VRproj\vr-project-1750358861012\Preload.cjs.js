"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),t=require("@react-three/fiber");function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=u(r);exports.Preload=function({all:r,scene:u,camera:n}){const a=t.useThree((({gl:e})=>e)),s=t.useThree((({camera:e})=>e)),i=t.useThree((({scene:e})=>e));return c.useLayoutEffect((()=>{const t=[];r&&(u||i).traverse((e=>{!1===e.visible&&(t.push(e),e.visible=!0)})),a.compile(u||i,n||s);const c=new e.WebGLCubeRenderTarget(128);new e.CubeCamera(.01,1e5,c).update(a,u||i),c.dispose(),t.forEach((e=>e.visible=!1))}),[]),null};
