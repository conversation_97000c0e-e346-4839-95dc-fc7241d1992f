{"version": 3, "file": "PosixModeBits.js", "sourceRoot": "", "sources": ["../src/PosixModeBits.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,qEAAqE;AACrE,+BAA+B;AAE/B;;;;;;;;;;;;;;GAcG;AACH,IAAY,aAwEX;AAxED,WAAY,aAAa;IACvB,WAAW;IAEX;;OAEG;IACH,2DAAiB,CAAA;IAEjB;;OAEG;IACH,6DAAkB,CAAA;IAElB;;;OAGG;IACH,gEAAoB,CAAA;IAEpB;;OAEG;IACH,4DAAkB,CAAA;IAElB;;OAEG;IACH,8DAAmB,CAAA;IAEnB;;;OAGG;IACH,iEAAqB,CAAA;IAErB;;OAEG;IACH,6DAAmB,CAAA;IAEnB;;OAEG;IACH,+DAAoB,CAAA;IAEpB;;;OAGG;IACH,mEAAsB,CAAA;IAEtB,kBAAkB;IAElB;;OAEG;IACH,iDAAQ,CAAA;IAER;;OAEG;IACH,yDAA2C,CAAA;IAE3C;;OAEG;IACH,2DAA+C,CAAA;IAE/C;;OAEG;IACH,8DAAuD,CAAA;AACzD,CAAC,EAxEW,aAAa,6BAAb,aAAa,QAwExB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n// The PosixModeBits are intended to be used with bitwise operations.\n/* eslint-disable no-bitwise */\n\n/**\n * An integer value used to specify file permissions for POSIX-like operating systems.\n *\n * @remarks\n *\n * This bitfield corresponds to the \"mode_t\" structure described in this document:\n * http://pubs.opengroup.org/onlinepubs/**********/basedefs/sys_stat.h.html\n *\n * It is used with NodeJS APIs such as fs.Stat.mode and fs.chmodSync().  These values\n * represent a set of permissions and can be combined using bitwise arithmetic.\n *\n * POSIX is a registered trademark of the Institute of Electrical and Electronic Engineers, Inc.\n *\n * @public\n */\nexport enum PosixModeBits {\n  // The bits\n\n  /**\n   * Indicates that the item's owner can read the item.\n   */\n  UserRead = 1 << 8,\n\n  /**\n   * Indicates that the item's owner can modify the item.\n   */\n  UserWrite = 1 << 7,\n\n  /**\n   * Indicates that the item's owner can execute the item (if it is a file)\n   * or search the item (if it is a directory).\n   */\n  UserExecute = 1 << 6,\n\n  /**\n   * Indicates that users belonging to the item's group can read the item.\n   */\n  GroupRead = 1 << 5,\n\n  /**\n   * Indicates that users belonging to the item's group can modify the item.\n   */\n  GroupWrite = 1 << 4,\n\n  /**\n   * Indicates that users belonging to the item's group can execute the item (if it is a file)\n   * or search the item (if it is a directory).\n   */\n  GroupExecute = 1 << 3,\n\n  /**\n   * Indicates that other users (besides the item's owner user or group) can read the item.\n   */\n  OthersRead = 1 << 2,\n\n  /**\n   * Indicates that other users (besides the item's owner user or group) can modify the item.\n   */\n  OthersWrite = 1 << 1,\n\n  /**\n   * Indicates that other users (besides the item's owner user or group) can execute the item (if it is a file)\n   * or search the item (if it is a directory).\n   */\n  OthersExecute = 1 << 0,\n\n  // Helpful aliases\n\n  /**\n   * A zero value where no permissions bits are set.\n   */\n  None = 0,\n\n  /**\n   * An alias combining OthersRead, GroupRead, and UserRead permission bits.\n   */\n  AllRead = OthersRead | GroupRead | UserRead,\n\n  /**\n   * An alias combining OthersWrite, GroupWrite, and UserWrite permission bits.\n   */\n  AllWrite = OthersWrite | GroupWrite | UserWrite,\n\n  /**\n   * An alias combining OthersExecute, GroupExecute, and UserExecute permission bits.\n   */\n  AllExecute = OthersExecute | GroupExecute | UserExecute\n}\n"]}