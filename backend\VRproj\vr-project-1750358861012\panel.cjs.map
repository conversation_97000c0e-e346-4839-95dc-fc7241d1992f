{"version": 3, "file": "panel.cjs", "sources": ["../lib/panel.ts"], "sourcesContent": null, "names": [], "mappings": ";;AAAA,MAAM,MAAM;AAAA,EAiBR,YAAY,MAAc,IAAY,IAAY;AAC9C,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,KAAK,KAAK,MAAM,OAAO,oBAAoB,CAAC;AAE5C,SAAA,QAAQ,KAAK,KAAK;AAClB,SAAA,SAAS,KAAK,KAAK;AACnB,SAAA,SAAS,IAAI,KAAK;AAClB,SAAA,SAAS,IAAI,KAAK;AAClB,SAAA,UAAU,IAAI,KAAK;AACnB,SAAA,UAAU,KAAK,KAAK;AACpB,SAAA,cAAc,KAAK,KAAK;AACxB,SAAA,eAAe,KAAK,KAAK;AAEzB,SAAA,SAAS,SAAS,cAAc,QAAQ;AACxC,SAAA,OAAO,QAAQ,KAAK;AACpB,SAAA,OAAO,SAAS,KAAK;AACrB,SAAA,OAAO,MAAM,QAAQ;AACrB,SAAA,OAAO,MAAM,SAAS;AACtB,SAAA,OAAO,MAAM,WAAW;AACxB,SAAA,OAAO,MAAM,UAAU;AAE5B,SAAK,UAAU,KAAK,OAAO,WAAW,IAAI;AAC1C,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EAEQ,iBAAiC;AACrC,QAAI,CAAC,KAAK;AAAe,YAAA,IAAI,MAAM,YAAY;AAEzC,UAAA,WAAW,KAAK,QAAQ;AAAA,MAC1B;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA,KAAK,UAAU,KAAK;AAAA,IAAA;AAGpB,QAAA;AACJ,UAAM,WAAmB,KAAK;AAEtB,YAAA,KAAK,GAAG,YAAe,GAAA;AAAA,MAC3B,KAAK;AACY,qBAAA;AACb;AAAA,MACJ,KAAK;AACY,qBAAA;AACb;AAAA,MACJ,KAAK;AACY,qBAAA;AACb;AAAA,MACJ,KAAK;AACY,qBAAA;AACb;AAAA,MACJ;AACI,qBAAa,KAAK;AAClB;AAAA,IACR;AAES,aAAA,aAAa,GAAG,UAAU;AAC1B,aAAA,aAAa,GAAG,QAAQ;AAE1B,WAAA;AAAA,EACX;AAAA,EAEQ,mBAAmB;AACvB,QAAI,CAAC,KAAK;AAAS;AAEnB,SAAK,QAAQ,OAAO,UAAW,IAAI,KAAK,KAAM;AAC9C,SAAK,QAAQ,eAAe;AAGvB,SAAA,WAAW,KAAK;AAGhB,SAAA,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ,SAAS,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AAG9C,SAAA,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ,SAAS,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM;AAGpD,SAAA,QAAQ,YAAY,KAAK;AACzB,SAAA,QAAQ,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa,KAAK,YAAY;AAGhF,SAAA,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ,cAAc;AACtB,SAAA,QAAQ,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa,KAAK,YAAY;AAAA,EACzF;AAAA,EAEA,OACI,OACA,YACA,UACA,UACA,WAAW,GACb;AACE,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK;AAAU;AAErC,UAAM,MAAM,KAAK,IAAI,UAAU,KAAK;AACpC,UAAM,MAAM,KAAK,IAAI,UAAU,KAAK;AACzB,eAAA,KAAK,IAAI,UAAU,UAAU;AAGxC,SAAK,QAAQ,cAAc;AACtB,SAAA,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ,SAAS,GAAG,GAAG,KAAK,OAAO,KAAK,OAAO;AAG/C,SAAA,QAAQ,YAAY,KAAK;AAC9B,SAAK,QAAQ;AAAA,MACT,GAAG,MAAM,QAAQ,QAAQ,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,CAAC,IAAI;AAAA,QACjE,IAAI,QAAQ,QAAQ;AAAA,MACvB,CAAA;AAAA,MACD,KAAK;AAAA,MACL,KAAK;AAAA,IAAA;AAIT,SAAK,QAAQ;AAAA,MACT,KAAK;AAAA,MACL,KAAK,UAAU,KAAK;AAAA,MACpB,KAAK;AAAA,MACL,KAAK,cAAc,KAAK;AAAA,MACxB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc,KAAK;AAAA,MACxB,KAAK;AAAA,IAAA;AAIT,UAAM,eAAe,KAAK,gBAAgB,IAAI,aAAa,YAAY,KAAK;AAE5E,QAAI,eAAe,GAAG;AAClB,WAAK,QAAQ,cAAc;AACtB,WAAA,QAAQ,YAAY,KAAK;AAC9B,WAAK,QAAQ;AAAA,QACT,KAAK,UAAU,KAAK,cAAc,KAAK;AAAA,QACvC,KAAK,UAAU,KAAK,eAAe;AAAA,QACnC,KAAK;AAAA,QACL;AAAA,MAAA;AAAA,IAER;AAAA,EACJ;AACJ;;"}