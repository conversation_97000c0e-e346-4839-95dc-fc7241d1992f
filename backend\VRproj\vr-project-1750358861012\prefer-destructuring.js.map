{"version": 3, "file": "prefer-destructuring.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-destructuring.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,oDAA0D;AAC1D,sDAAwC;AAOxC,kCAAuE;AACvE,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,sBAAsB,CAAC,CAAC;AAU3D,MAAM,uBAAuB,GAAgB;IAC3C,IAAI,EAAE,QAAQ;IACd,oBAAoB,EAAE,KAAK;IAC3B,UAAU,EAAE;QACV,KAAK,EAAE;YACL,IAAI,EAAE,SAAS;SAChB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,MAAM,GAA2B;IACrC;QACE,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,oBAAoB,EAAE,uBAAuB;oBAC7C,kBAAkB,EAAE,uBAAuB;iBAC5C;aACF;YACD,uBAAuB;SACxB;KACF;IACD;QACE,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,uCAAuC,EAAE;gBACvC,IAAI,EAAE,SAAS;gBACf,WAAW,EACT,kFAAkF;aACrF;YACD,2BAA2B,EAAE;gBAC3B,IAAI,EAAE,SAAS;gBACf,WAAW,EACT,6FAA6F;aAChG;SACF;KACF;CACF,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,2DAA2D;QAC3D,IAAI,EAAE;YACJ,WAAW,EAAE,kDAAkD;YAC/D,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,MAAM;KACP;IACD,cAAc,EAAE;QACd;YACE,oBAAoB,EAAE;gBACpB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;YACD,kBAAkB,EAAE;gBAClB,KAAK,EAAE,IAAI;gBACX,MAAM,EAAE,IAAI;aACb;SACF;QACD,EAAE;KACH;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC;QACrC,MAAM,EACJ,uCAAuC,GAAG,KAAK,EAC/C,2BAA2B,GAAG,KAAK,GACpC,GAAG,OAAO,CAAC;QACZ,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,wBAAwB,GAA4B,IAAI,CAAC;QAE7D,OAAO;YACL,oBAAoB,CAAC,IAAI;gBACvB,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;oBAC1B,OAAO;gBACT,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC5C,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACrB,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC;SACF,CAAC;QAEF,SAAS,YAAY,CACnB,QAAoD,EACpD,SAAqC,EACrC,UAAuE;YAEvE,MAAM,KAAK,GACT,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC3C,QAAQ,CAAC,cAAc,IAAI,IAAI;gBAC7B,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IACE,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;gBAC5C,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC3C,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;gBACjD,QAAQ,CAAC,cAAc,IAAI,IAAI;gBAC/B,CAAC,uCAAuC,EACxC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IACE,SAAS,IAAI,IAAI;gBACjB,gCAAgC,CAAC,SAAS,CAAC;gBAC3C,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,KAAK,EAC9C,CAAC;gBACD,MAAM,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC1D,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC;oBACnD,IACE,CAAC,2BAA2B;wBAC5B,CAAC,wBAAwB,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,EACpD,CAAC;wBACD,OAAO;oBACT,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,qBAAqB;wBAChC,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACzB,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YAED,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB,EAAE,CAAC;gBAC5D,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,SAAS,wBAAwB,CAC/B,QAEqC,EACrC,iBAAqC;YAErC,IAAI,QAAQ,IAAI,YAAY,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;gBACxD,OAAO,YAAY,CAAC,iBAAiB,CAAC,CAAC;YACzC,CAAC;YACD,OAAO,YAAY,CAAC,QAAqC,CAAC,CACxD,iBAA2E,CAC5E,CAAC;QACJ,CAAC;QAED,SAAS,mBAAmB;YAC1B,wBAAwB,KAAK,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;YACpE,OAAO,wBAAwB,CAAC;QAClC,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAIH,SAAS,YAAY,CAAC,OAAgB;IACpC,MAAM,aAAa,GAEf;QACF,MAAM,EAAE,CAAC,UAAU,EAAQ,EAAE;YAC3B,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,UAAU;gBACb,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,sFAAsF;IACtF,iEAAiE;IACjE,8DAA8D;IAC9D,OAAO,IAAI,KAAK,CAAU,aAA+B,EAAE;QACzD,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ;YACxB,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,uBAAuB,CAC9B,IAAa,EACb,WAA2B;IAE3B,IAAI,IAAA,oBAAa,EAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;QACpB,MAAM,QAAQ,GAAG,OAAO,CAAC,gCAAgC,CACvD,IAAI,EACJ,UAAU,EACV,WAAW,CACZ,CAAC;QACF,OAAO,QAAQ,IAAI,IAAI,CAAC;IAC1B,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,uBAAuB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,gCAAgC,CACvC,IAAyB;IAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;QAClD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;QAClD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/C,CAAC"}