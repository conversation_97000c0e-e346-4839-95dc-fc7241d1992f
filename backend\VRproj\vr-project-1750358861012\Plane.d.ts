import { Mesh } from 'three';
export declare class XRPlaneModel extends Mesh {
    constructor(plane: XRPlane);
}
ceiling' | 'wall' | 'door' | 'window' | 'other' | string;
    interface XRPlane {
        semanticLabel?: XRSemanticLabel;
    }
    interface XRMesh {
        semanticLabel?: XRSemanticLabel;
    }
}
export declare function updateXRPlaneGeometry(plane: XRPlane, geometry: (BufferGeometry & {
    createdAt?: number;
}) | undefined): BufferGeometry & {
    createdAt?: number;
};
