import * as ts from 'typescript';
import type { Collector } from '../collector/Collector';
export declare class PackageDocComment {
    /**
     * For the given source file, see if it starts with a TSDoc comment containing the `@packageDocumentation` tag.
     */
    static tryFindInSourceFile(sourceFile: ts.SourceFile, collector: Collector): ts.TextRange | undefined;
}
//# sourceMappingURL=PackageDocComment.d.ts.map