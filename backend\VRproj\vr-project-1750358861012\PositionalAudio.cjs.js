"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three"),u=require("@react-three/fiber");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var u=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,u.get?u:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var c=n(e),s=o(r);const a=s.forwardRef((({url:e,distance:r=1,loop:n=!0,autoplay:o,...a},i)=>{const f=s.useRef(null);s.useImperativeHandle(i,(()=>f.current),[]);const l=u.useThree((({camera:e})=>e)),[d]=s.useState((()=>new t.AudioListener)),p=u.useLoader(t.AudioLoader,e);return s.useEffect((()=>{const e=f.current;e&&(e.setBuffer(p),e.setRefDistance(r),e.setLoop(n),o&&!e.isPlaying&&e.play())}),[p,l,r,n]),s.useEffect((()=>{const e=f.current;return l.add(d),()=>{l.remove(d),e&&(e.isPlaying&&e.stop(),e.source&&e.source._connected&&e.disconnect())}}),[]),s.createElement("positionalAudio",c.default({ref:f,args:[d]},a))}));exports.PositionalAudio=a;
