{"version": 3, "file": "ParserMessageLog.js", "sourceRoot": "", "sources": ["../../src/parser/ParserMessageLog.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAOhD;;GAEG;AACH;IAAA;QACU,cAAS,GAAoB,EAAE,CAAC;IA0E1C,CAAC;IArEC,sBAAW,sCAAQ;QAHnB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED;;OAEG;IACI,qCAAU,GAAjB,UAAkB,aAA4B;QAC5C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,iDAAsB,GAA7B,UAA8B,SAAyB,EAAE,WAAmB,EAAE,SAAoB;QAChG,IAAI,CAAC,UAAU,CACb,IAAI,aAAa,CAAC;YAChB,SAAS,WAAA;YACT,WAAW,aAAA;YACX,SAAS,WAAA;SACV,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,qDAA0B,GAAjC,UACE,SAAyB,EACzB,WAAmB,EACnB,aAA4B,EAC5B,OAAiB;QAEjB,IAAI,CAAC,UAAU,CACb,IAAI,aAAa,CAAC;YAChB,SAAS,WAAA;YACT,WAAW,aAAA;YACX,SAAS,EAAE,aAAa,CAAC,sBAAsB,EAAE;YACjD,aAAa,eAAA;YACb,OAAO,SAAA;SACR,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,oDAAyB,GAAhC,UAAiC,YAA0B;QACzD,IAAI,aAA4B,CAAC;QAEjC,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,oFAAoF;YACpF,aAAa,GAAG,YAAY,CAAC,WAAW,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,yFAAyF;YACzF,gEAAgE;YAChE,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,UAAU,CACb,IAAI,aAAa,CAAC;YAChB,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,WAAW,EAAE,YAAY,CAAC,YAAY;YACtC,SAAS,EAAE,aAAa,CAAC,sBAAsB,EAAE;YACjD,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,YAAY;SACtB,CAAC,CACH,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AA3ED,IA2EC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { ParserMessage } from './ParserMessage';\r\nimport type { TextRange } from './TextRange';\r\nimport type { TokenSequence } from './TokenSequence';\r\nimport type { DocNode } from '../nodes/DocNode';\r\nimport type { DocErrorText } from '../nodes/DocErrorText';\r\nimport type { TSDocMessageId } from './TSDocMessageId';\r\n\r\n/**\r\n * Used to report errors and warnings that occurred during parsing.\r\n */\r\nexport class ParserMessageLog {\r\n  private _messages: ParserMessage[] = [];\r\n\r\n  /**\r\n   * The unfiltered list of all messages.\r\n   */\r\n  public get messages(): ReadonlyArray<ParserMessage> {\r\n    return this._messages;\r\n  }\r\n\r\n  /**\r\n   * Append a message to the log.\r\n   */\r\n  public addMessage(parserMessage: ParserMessage): void {\r\n    this._messages.push(parserMessage);\r\n  }\r\n\r\n  /**\r\n   * Append a message associated with a TextRange.\r\n   */\r\n  public addMessageForTextRange(messageId: TSDocMessageId, messageText: string, textRange: TextRange): void {\r\n    this.addMessage(\r\n      new ParserMessage({\r\n        messageId,\r\n        messageText,\r\n        textRange\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Append a message associated with a TokenSequence.\r\n   */\r\n  public addMessageForTokenSequence(\r\n    messageId: TSDocMessageId,\r\n    messageText: string,\r\n    tokenSequence: TokenSequence,\r\n    docNode?: DocNode\r\n  ): void {\r\n    this.addMessage(\r\n      new ParserMessage({\r\n        messageId,\r\n        messageText,\r\n        textRange: tokenSequence.getContainingTextRange(),\r\n        tokenSequence,\r\n        docNode\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Append a message associated with a TokenSequence.\r\n   */\r\n  public addMessageForDocErrorText(docErrorText: DocErrorText): void {\r\n    let tokenSequence: TokenSequence;\r\n\r\n    if (docErrorText.textExcerpt) {\r\n      // If there is an excerpt directly associated with the DocErrorText, highlight that:\r\n      tokenSequence = docErrorText.textExcerpt;\r\n    } else {\r\n      // Otherwise we can use the errorLocation, but typically that is meant to give additional\r\n      // details, not to indicate the primary location of the problem.\r\n      tokenSequence = docErrorText.errorLocation;\r\n    }\r\n\r\n    this.addMessage(\r\n      new ParserMessage({\r\n        messageId: docErrorText.messageId,\r\n        messageText: docErrorText.errorMessage,\r\n        textRange: tokenSequence.getContainingTextRange(),\r\n        tokenSequence: tokenSequence,\r\n        docNode: docErrorText\r\n      })\r\n    );\r\n  }\r\n}\r\n"]}