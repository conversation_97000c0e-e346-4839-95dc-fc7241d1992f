{"version": 3, "file": "ProtectableMap.js", "sourceRoot": "", "sources": ["../src/ProtectableMap.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,6DAA0D;AA4B1D;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAa,cAAc;IAGzB,YAAmB,UAA2C;QAC5D,IAAI,CAAC,cAAc,GAAG,IAAI,uCAAkB,CAAO,IAAI,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,8EAA8E;IAC9E,qDAAqD;IAErD;;;OAGG;IACI,KAAK;QACV,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,GAAM;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,GAAM,EAAE,KAAQ;QACzB,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8EAA8E;IAC9E,oDAAoD;IAEpD;;OAEG;IACH,8DAA8D;IACvD,OAAO,CAAC,UAAsD,EAAE,OAAa;QAClF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,GAAM;QACf,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,GAAM;QACf,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAClC,CAAC;CACF;AA3ED,wCA2EC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport { ProtectableMapView } from './ProtectableMapView';\n\n/**\n * Constructor parameters for {@link ProtectableMap}\n *\n * @public\n */\nexport interface IProtectableMapParameters<K, V> {\n  /**\n   * An optional hook that will be invoked before Map.clear() is performed.\n   */\n  onClear?: (source: ProtectableMap<K, V>) => void;\n\n  /**\n   * An optional hook that will be invoked before Map.delete() is performed.\n   */\n  onDelete?: (source: ProtectableMap<K, V>, key: K) => void;\n\n  /**\n   * An optional hook that will be invoked before Map.set() is performed.\n   * @remarks\n   * If this hook is provided, the function MUST return the `value` parameter.\n   * This provides the opportunity to modify the value before it is added\n   * to the map.\n   */\n  onSet?: (source: ProtectableMap<K, V>, key: K, value: V) => V;\n}\n\n/**\n * The ProtectableMap provides an easy way for an API to expose a `Map<K, V>` property\n * while intercepting and validating any write operations that are performed by\n * consumers of the API.\n *\n * @remarks\n * The ProtectableMap itself is intended to be a private object that only its owner\n * can access directly.  Any operations performed directly on the ProtectableMap will\n * bypass the hooks and any validation they perform.  The public property that is exposed\n * to API consumers should return {@link ProtectableMap.protectedView} instead.\n *\n * For example, suppose you want to share your `Map<string, number>` data structure,\n * but you want to enforce that the key must always be an upper case string:\n * You could use the onSet() hook to validate the keys and throw an exception\n * if the key is not uppercase.\n *\n * @public\n */\nexport class ProtectableMap<K, V> {\n  private readonly _protectedView: ProtectableMapView<K, V>;\n\n  public constructor(parameters: IProtectableMapParameters<K, V>) {\n    this._protectedView = new ProtectableMapView<K, V>(this, parameters);\n  }\n\n  /**\n   * The owner of the protectable map should return this object via its public API.\n   */\n  public get protectedView(): Map<K, V> {\n    return this._protectedView;\n  }\n\n  // ---------------------------------------------------------------------------\n  // lib.es2015.collections contract - write operations\n\n  /**\n   * Removes all entries from the map.\n   * This operation does NOT invoke the ProtectableMap onClear() hook.\n   */\n  public clear(): void {\n    this._protectedView._clearUnprotected();\n  }\n\n  /**\n   * Removes the specified key from the map.\n   * This operation does NOT invoke the ProtectableMap onDelete() hook.\n   */\n  public delete(key: K): boolean {\n    return this._protectedView._deleteUnprotected(key);\n  }\n\n  /**\n   * Sets a value for the specified key.\n   * This operation does NOT invoke the ProtectableMap onSet() hook.\n   */\n  public set(key: K, value: V): this {\n    this._protectedView._setUnprotected(key, value);\n    return this;\n  }\n\n  // ---------------------------------------------------------------------------\n  // lib.es2015.collections contract - read operations\n\n  /**\n   * Performs an operation for each (key, value) entries in the map.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public forEach(callbackfn: (value: V, key: K, map: Map<K, V>) => void, thisArg?: any): void {\n    this._protectedView.forEach(callbackfn);\n  }\n\n  /**\n   * Retrieves the value for the specified key.\n   * @returns undefined if the value is undefined OR if the key is missing;\n   * otherwise returns the value associated with the key.\n   */\n  public get(key: K): V | undefined {\n    return this._protectedView.get(key);\n  }\n\n  /**\n   * Returns true if the specified key belongs to the map.\n   */\n  public has(key: K): boolean {\n    return this._protectedView.has(key);\n  }\n\n  /**\n   * Returns the number of (key, value) entries in the map.\n   */\n  public get size(): number {\n    return this._protectedView.size;\n  }\n}\n"]}