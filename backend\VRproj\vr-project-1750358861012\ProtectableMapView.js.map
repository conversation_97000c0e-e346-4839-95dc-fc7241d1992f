{"version": 3, "file": "ProtectableMapView.js", "sourceRoot": "", "sources": ["../src/ProtectableMapView.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D;;;;;;GAMG;AACH,MAAa,kBAAyB,SAAQ,GAAS;IAIrD,YAAmB,KAA2B,EAAE,UAA2C;QACzF,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAEe,KAAK;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QACD,KAAK,CAAC,KAAK,EAAE,CAAC;IAChB,CAAC;IAEe,MAAM,CAAC,GAAM;QAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAEe,GAAG,CAAC,GAAM,EAAE,KAAQ;QAClC,IAAI,aAAa,GAAM,KAAK,CAAC;QAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;QAC1E,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,sBAAsB;IACf,iBAAiB;QACtB,KAAK,CAAC,KAAK,EAAE,CAAC;IAChB,CAAC;IAED,sBAAsB;IACf,kBAAkB,CAAC,GAAM;QAC9B,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,sBAAsB;IACf,eAAe,CAAC,GAAM,EAAE,KAAQ;QACrC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACxB,CAAC;CACF;AAhDD,gDAgDC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport type { ProtectableMap, IProtectableMapParameters } from './ProtectableMap';\n\n/**\n * The internal wrapper used by ProtectableMap.  It extends the real `Map<K, V>` base class,\n * but hooks the destructive operations (clear/delete/set) to give the owner a chance\n * to block them.\n *\n * NOTE: This is not a public API.\n */\nexport class ProtectableMapView<K, V> extends Map<K, V> {\n  private readonly _owner: ProtectableMap<K, V>;\n  private readonly _parameters: IProtectableMapParameters<K, V>;\n\n  public constructor(owner: ProtectableMap<K, V>, parameters: IProtectableMapParameters<K, V>) {\n    super();\n\n    this._owner = owner;\n    this._parameters = parameters;\n  }\n\n  public override clear(): void {\n    if (this._parameters.onClear) {\n      this._parameters.onClear(this._owner);\n    }\n    super.clear();\n  }\n\n  public override delete(key: K): boolean {\n    if (this._parameters.onDelete) {\n      this._parameters.onDelete(this._owner, key);\n    }\n    return super.delete(key);\n  }\n\n  public override set(key: K, value: V): this {\n    let modifiedValue: V = value;\n    if (this._parameters.onSet) {\n      modifiedValue = this._parameters.onSet(this._owner, key, modifiedValue);\n    }\n    super.set(key, modifiedValue);\n    return this;\n  }\n\n  // INTERNAL USAGE ONLY\n  public _clearUnprotected(): void {\n    super.clear();\n  }\n\n  // INTERNAL USAGE ONLY\n  public _deleteUnprotected(key: K): boolean {\n    return super.delete(key);\n  }\n\n  // INTERNAL USAGE ONLY\n  public _setUnprotected(key: K, value: V): void {\n    super.set(key, value);\n  }\n}\n"]}