!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,n.registerPromiseWorker=e()}}(function(){return function e(n,r,t){function o(i,s){if(!r[i]){if(!n[i]){var u="function"==typeof require&&require;if(!s&&u)return u(i,!0);if(f)return f(i,!0);var c=new Error("Cannot find module '"+i+"'");throw c.code="MODULE_NOT_FOUND",c}var a=r[i]={exports:{}};n[i][0].call(a.exports,function(e){var r=n[i][1][e];return o(r?r:e)},a,a.exports,e,n,r,t)}return r[i].exports}for(var f="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}({1:[function(e,n,r){function t(e){return!!e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}n.exports=t},{}],2:[function(e,n,r){"use strict";function t(e){function n(e,n,r,t){function o(n,r){"function"!=typeof self.postMessage?e.ports[0].postMessage(n,r):self.postMessage(n,r)}r?("undefined"!=typeof console&&"error"in console&&console.error("Worker caught an error:",r),o([n,{message:r.message}])):t instanceof s?o([n,null,t.message],t.transferList):o([n,null,t])}function r(e,n){try{return{res:e(n,t)}}catch(e){return{err:e}}}function t(e,n){return new s(e,n)}function f(e,t,f,i){var s=r(t,i);s.err?n(e,f,s.err):o(s.res)?s.res.then(function(r){n(e,f,null,r)},function(r){n(e,f,r)}):n(e,f,null,s.res)}function i(r){var t=r.data;if(Array.isArray(t)&&2===t.length){var o=t[0],i=t[1];"function"!=typeof e?n(r,o,new Error("Please pass a function into register().")):f(r,e,o,i)}}function s(e,n){this.message=e,this.transferList=n}self.addEventListener("message",i)}var o=e(1);n.exports=t},{1:1}]},{},[2])(2)});
