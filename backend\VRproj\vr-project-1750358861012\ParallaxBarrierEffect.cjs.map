{"version": 3, "file": "ParallaxBarrierEffect.cjs", "sources": ["../../src/effects/ParallaxBarrierEffect.js"], "sourcesContent": ["import {\n  LinearFilter,\n  Mesh,\n  NearestFilter,\n  OrthographicCamera,\n  PlaneGeometry,\n  RGBAFormat,\n  Scene,\n  ShaderMaterial,\n  StereoCamera,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass ParallaxBarrierEffect {\n  constructor(renderer) {\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n\n    const _scene = new Scene()\n\n    const _stereo = new StereoCamera()\n\n    const _params = { minFilter: LinearFilter, magFilter: NearestFilter, format: RGBAFormat }\n\n    const _renderTargetL = new WebGLRenderTarget(512, 512, _params)\n    const _renderTargetR = new WebGLRenderTarget(512, 512, _params)\n\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: { value: _renderTargetL.texture },\n        mapRight: { value: _renderTargetR.texture },\n      },\n\n      vertexShader: [\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvUv = vec2( uv.x, uv.y );',\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        'uniform sampler2D mapLeft;',\n        'uniform sampler2D mapRight;',\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvec2 uv = vUv;',\n\n        '\tif ( ( mod( gl_FragCoord.y, 2.0 ) ) > 1.00 ) {',\n\n        '\t\tgl_FragColor = texture2D( mapLeft, uv );',\n\n        '\t} else {',\n\n        '\t\tgl_FragColor = texture2D( mapRight, uv );',\n\n        '\t}',\n\n        '\t#include <tonemapping_fragment>',\n        `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const mesh = new Mesh(new PlaneGeometry(2, 2), _material)\n    _scene.add(mesh)\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n\n      const pixelRatio = renderer.getPixelRatio()\n\n      _renderTargetL.setSize(width * pixelRatio, height * pixelRatio)\n      _renderTargetR.setSize(width * pixelRatio, height * pixelRatio)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.setRenderTarget(_renderTargetL)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setRenderTarget(_renderTargetR)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setRenderTarget(null)\n      renderer.render(_scene, _camera)\n    }\n  }\n}\n\nexport { ParallaxBarrierEffect }\n"], "names": ["OrthographicCamera", "Scene", "StereoCamera", "LinearFilter", "NearestFilter", "RGBAFormat", "WebGLRenderTarget", "ShaderMaterial", "version", "<PERSON><PERSON>", "PlaneGeometry"], "mappings": ";;;;AAcA,MAAM,sBAAsB;AAAA,EAC1B,YAAY,UAAU;AACpB,UAAM,UAAU,IAAIA,MAAkB,mBAAC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;AAEzD,UAAM,SAAS,IAAIC,YAAO;AAE1B,UAAM,UAAU,IAAIC,mBAAc;AAElC,UAAM,UAAU,EAAE,WAAWC,MAAAA,cAAc,WAAWC,MAAa,eAAE,QAAQC,iBAAY;AAEzF,UAAM,iBAAiB,IAAIC,MAAAA,kBAAkB,KAAK,KAAK,OAAO;AAC9D,UAAM,iBAAiB,IAAIA,MAAAA,kBAAkB,KAAK,KAAK,OAAO;AAE9D,UAAM,YAAY,IAAIC,qBAAe;AAAA,MACnC,UAAU;AAAA,QACR,SAAS,EAAE,OAAO,eAAe,QAAS;AAAA,QAC1C,UAAU,EAAE,OAAO,eAAe,QAAS;AAAA,MAC5C;AAAA,MAED,cAAc;AAAA,QACZ;AAAA,QAEA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,MAEX,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QACA,cAAcC,UAAAA,WAAW,MAAM,wBAAwB;AAAA,QAEvD;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,IACjB,CAAK;AAED,UAAM,OAAO,IAAIC,WAAK,IAAIC,MAAa,cAAC,GAAG,CAAC,GAAG,SAAS;AACxD,WAAO,IAAI,IAAI;AAEf,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,eAAS,QAAQ,OAAO,MAAM;AAE9B,YAAM,aAAa,SAAS,cAAe;AAE3C,qBAAe,QAAQ,QAAQ,YAAY,SAAS,UAAU;AAC9D,qBAAe,QAAQ,QAAQ,YAAY,SAAS,UAAU;AAAA,IAC/D;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AAEnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,cAAQ,OAAO,MAAM;AAErB,eAAS,gBAAgB,cAAc;AACvC,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,gBAAgB,cAAc;AACvC,eAAS,MAAO;AAChB,eAAS,OAAO,OAAO,QAAQ,OAAO;AAEtC,eAAS,gBAAgB,IAAI;AAC7B,eAAS,OAAO,QAAQ,OAAO;AAAA,IAChC;AAAA,EACF;AACH;;"}