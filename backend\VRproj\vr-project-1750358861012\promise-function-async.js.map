{"version": 3, "file": "promise-function-async.js", "sourceRoot": "", "sources": ["../../src/rules/promise-function-async.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA2E;AAC3E,+CAAiC;AAEjC,kCAQiB;AAcjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,YAAY,EAAE,+CAA+C;SAC9D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,yDAAyD;qBAC5D;oBACD,mBAAmB,EAAE;wBACnB,IAAI,EAAE,OAAO;wBACb,WAAW,EACT,qEAAqE;wBACvE,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;oBACD,mBAAmB,EAAE;wBACnB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,mCAAmC;qBACjD;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,oDAAoD;qBAClE;oBACD,wBAAwB,EAAE;wBACxB,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,8CAA8C;qBAC5D;oBACD,uBAAuB,EAAE;wBACvB,IAAI,EAAE,SAAS;wBACf,WAAW,EACT,0DAA0D;qBAC7D;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,QAAQ,EAAE,IAAI;YACd,mBAAmB,EAAE,EAAE;YACvB,mBAAmB,EAAE,IAAI;YACzB,yBAAyB,EAAE,IAAI;YAC/B,wBAAwB,EAAE,IAAI;YAC9B,uBAAuB,EAAE,IAAI;SAC9B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,QAAQ,EACR,mBAAmB,EACnB,mBAAmB,EACnB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,GACxB,EACF;QAED,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;YACrC,SAAS;YACT,qEAAqE;YACrE,oEAAoE;YACpE,GAAG,mBAAoB;SACxB,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,YAAY,CACnB,IAG+B;YAE/B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B,EAAE,CAAC;gBACnE,iCAAiC;gBACjC,OAAO;YACT,CAAC;YAED,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAAC;gBACvD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,EAC1D,CAAC;gBACD,qCAAqC;gBACrC,OAAO;YACT,CAAC;YAED,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAC7C,OAAO,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAC5C,CAAC;YAEF,IACE,CAAC,QAAQ;gBACT,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtB,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAC7D,EACD,CAAC;gBACD,+DAA+D;gBAC/D,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;oBACjD,IAAI;oBACJ,SAAS,EAAE,cAAc;iBAC1B,CAAC,CAAC;YACL,CAAC;YAED;YACE,+DAA+D;YAC/D,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACvB,IAAA,6BAAsB,EACpB,IAAI,EACJ,IAAI,EACJ,sBAAsB;YACtB,qIAAqI;YACrI,IAAI,CAAC,UAAU,IAAI,IAAI,CACxB,CACF,EACD,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;oBACjD,IAAI;oBACJ,SAAS,EAAE,cAAc;oBACzB,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BACpD,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;gCAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EACrB,CAAC;4BACD,wEAAwE;4BACxE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;4BAE3B,kCAAkC;4BAClC,IAAI,QAAQ,GAAG,IAAA,iBAAU,EACvB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,EACxC,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CACtD,CAAC;4BAEF,8CAA8C;4BAC9C,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gCAC/C,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB,CAAC;gCACD,MAAM,aAAa,GACjB,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gCAClD,QAAQ,GAAG,IAAA,iBAAU,EACnB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,EAC/C,wBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC9D,CAAC;4BACJ,CAAC;4BAED,uEAAuE;4BACvE,OACE,QAAQ,CAAC,IAAI,KAAK,uBAAe,CAAC,OAAO;gCACzC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EACvC,CAAC;gCACD,QAAQ,GAAG,IAAA,iBAAU,EACnB,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC1C,wBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CACnD,CAAC;4BACJ,CAAC;4BAED,2DAA2D;4BAC3D,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,cAAc,CACpD,IAAA,iBAAU,EACR,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC3C,wBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CACnD,EACD,QAAQ,CACT,CAAC;4BAEF,IAAI,IAAI,GAAG,QAAQ,CAAC;4BACpB,IAAI,WAAW,EAAE,CAAC;gCAChB,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;4BACpB,CAAC;4BACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;wBAChD,CAAC;wBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAChD,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,GAAG,CAAC,mBAAmB,IAAI;gBACzB,wCAAwC,CACtC,IAAsC;oBAEtC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;YACF,GAAG,CAAC,yBAAyB,IAAI;gBAC/B,oCAAoC,CAClC,IAAkC;oBAElC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;YACF,mCAAmC,CACjC,IAAiC;gBAEjC,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACpD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAC7B,CAAC;oBACD,IAAI,uBAAuB,EAAE,CAAC;wBAC5B,YAAY,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,wBAAwB,EAAE,CAAC;oBAC7B,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}