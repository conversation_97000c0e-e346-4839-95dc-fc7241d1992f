{"version": 3, "file": "Projector.js", "sources": ["../../src/renderers/Projector.js"], "sourcesContent": ["import { Box3, <PERSON>, <PERSON>S<PERSON>, <PERSON>ust<PERSON>, Matrix3, Matrix4, Vector2, Vector3, Vector4 } from 'three'\n\nclass RenderableObject {\n  constructor() {\n    this.id = 0\n\n    this.object = null\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableFace {\n  constructor() {\n    this.id = 0\n\n    this.v1 = new RenderableVertex()\n    this.v2 = new RenderableVertex()\n    this.v3 = new RenderableVertex()\n\n    this.normalModel = new Vector3()\n\n    this.vertexNormalsModel = [new Vector3(), new Vector3(), new Vector3()]\n    this.vertexNormalsLength = 0\n\n    this.color = new Color()\n    this.material = null\n    this.uvs = [new Vector2(), new Vector2(), new Vector2()]\n\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableVertex {\n  constructor() {\n    this.position = new Vector3()\n    this.positionWorld = new Vector3()\n    this.positionScreen = new Vector4()\n\n    this.visible = true\n  }\n\n  copy(vertex) {\n    this.positionWorld.copy(vertex.positionWorld)\n    this.positionScreen.copy(vertex.positionScreen)\n  }\n}\n\n//\n\nclass RenderableLine {\n  constructor() {\n    this.id = 0\n\n    this.v1 = new RenderableVertex()\n    this.v2 = new RenderableVertex()\n\n    this.vertexColors = [new Color(), new Color()]\n    this.material = null\n\n    this.z = 0\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass RenderableSprite {\n  constructor() {\n    this.id = 0\n\n    this.object = null\n\n    this.x = 0\n    this.y = 0\n    this.z = 0\n\n    this.rotation = 0\n    this.scale = new Vector2()\n\n    this.material = null\n    this.renderOrder = 0\n  }\n}\n\n//\n\nclass Projector {\n  constructor() {\n    let _object,\n      _objectCount,\n      _objectPoolLength = 0,\n      _vertex,\n      _vertexCount,\n      _vertexPoolLength = 0,\n      _face,\n      _faceCount,\n      _facePoolLength = 0,\n      _line,\n      _lineCount,\n      _linePoolLength = 0,\n      _sprite,\n      _spriteCount,\n      _spritePoolLength = 0,\n      _modelMatrix\n\n    const _renderData = { objects: [], lights: [], elements: [] },\n      _vector3 = new Vector3(),\n      _vector4 = new Vector4(),\n      _clipBox = new Box3(new Vector3(-1, -1, -1), new Vector3(1, 1, 1)),\n      _boundingBox = new Box3(),\n      _points3 = new Array(3),\n      _viewMatrix = new Matrix4(),\n      _viewProjectionMatrix = new Matrix4(),\n      _modelViewProjectionMatrix = new Matrix4(),\n      _frustum = new Frustum(),\n      _objectPool = [],\n      _vertexPool = [],\n      _facePool = [],\n      _linePool = [],\n      _spritePool = []\n\n    //\n\n    function RenderList() {\n      const normals = []\n      const colors = []\n      const uvs = []\n\n      let object = null\n\n      const normalMatrix = new Matrix3()\n\n      function setObject(value) {\n        object = value\n\n        normalMatrix.getNormalMatrix(object.matrixWorld)\n\n        normals.length = 0\n        colors.length = 0\n        uvs.length = 0\n      }\n\n      function projectVertex(vertex) {\n        const position = vertex.position\n        const positionWorld = vertex.positionWorld\n        const positionScreen = vertex.positionScreen\n\n        positionWorld.copy(position).applyMatrix4(_modelMatrix)\n        positionScreen.copy(positionWorld).applyMatrix4(_viewProjectionMatrix)\n\n        const invW = 1 / positionScreen.w\n\n        positionScreen.x *= invW\n        positionScreen.y *= invW\n        positionScreen.z *= invW\n\n        vertex.visible =\n          positionScreen.x >= -1 &&\n          positionScreen.x <= 1 &&\n          positionScreen.y >= -1 &&\n          positionScreen.y <= 1 &&\n          positionScreen.z >= -1 &&\n          positionScreen.z <= 1\n      }\n\n      function pushVertex(x, y, z) {\n        _vertex = getNextVertexInPool()\n        _vertex.position.set(x, y, z)\n\n        projectVertex(_vertex)\n      }\n\n      function pushNormal(x, y, z) {\n        normals.push(x, y, z)\n      }\n\n      function pushColor(r, g, b) {\n        colors.push(r, g, b)\n      }\n\n      function pushUv(x, y) {\n        uvs.push(x, y)\n      }\n\n      function checkTriangleVisibility(v1, v2, v3) {\n        if (v1.visible === true || v2.visible === true || v3.visible === true) return true\n\n        _points3[0] = v1.positionScreen\n        _points3[1] = v2.positionScreen\n        _points3[2] = v3.positionScreen\n\n        return _clipBox.intersectsBox(_boundingBox.setFromPoints(_points3))\n      }\n\n      function checkBackfaceCulling(v1, v2, v3) {\n        return (\n          (v3.positionScreen.x - v1.positionScreen.x) * (v2.positionScreen.y - v1.positionScreen.y) -\n            (v3.positionScreen.y - v1.positionScreen.y) * (v2.positionScreen.x - v1.positionScreen.x) <\n          0\n        )\n      }\n\n      function pushLine(a, b) {\n        const v1 = _vertexPool[a]\n        const v2 = _vertexPool[b]\n\n        // Clip\n\n        v1.positionScreen.copy(v1.position).applyMatrix4(_modelViewProjectionMatrix)\n        v2.positionScreen.copy(v2.position).applyMatrix4(_modelViewProjectionMatrix)\n\n        if (clipLine(v1.positionScreen, v2.positionScreen) === true) {\n          // Perform the perspective divide\n          v1.positionScreen.multiplyScalar(1 / v1.positionScreen.w)\n          v2.positionScreen.multiplyScalar(1 / v2.positionScreen.w)\n\n          _line = getNextLineInPool()\n          _line.id = object.id\n          _line.v1.copy(v1)\n          _line.v2.copy(v2)\n          _line.z = Math.max(v1.positionScreen.z, v2.positionScreen.z)\n          _line.renderOrder = object.renderOrder\n\n          _line.material = object.material\n\n          if (object.material.vertexColors) {\n            _line.vertexColors[0].fromArray(colors, a * 3)\n            _line.vertexColors[1].fromArray(colors, b * 3)\n          }\n\n          _renderData.elements.push(_line)\n        }\n      }\n\n      function pushTriangle(a, b, c, material) {\n        const v1 = _vertexPool[a]\n        const v2 = _vertexPool[b]\n        const v3 = _vertexPool[c]\n\n        if (checkTriangleVisibility(v1, v2, v3) === false) return\n\n        if (material.side === DoubleSide || checkBackfaceCulling(v1, v2, v3) === true) {\n          _face = getNextFaceInPool()\n\n          _face.id = object.id\n          _face.v1.copy(v1)\n          _face.v2.copy(v2)\n          _face.v3.copy(v3)\n          _face.z = (v1.positionScreen.z + v2.positionScreen.z + v3.positionScreen.z) / 3\n          _face.renderOrder = object.renderOrder\n\n          // face normal\n          _vector3.subVectors(v3.position, v2.position)\n          _vector4.subVectors(v1.position, v2.position)\n          _vector3.cross(_vector4)\n          _face.normalModel.copy(_vector3)\n          _face.normalModel.applyMatrix3(normalMatrix).normalize()\n\n          for (let i = 0; i < 3; i++) {\n            const normal = _face.vertexNormalsModel[i]\n            normal.fromArray(normals, arguments[i] * 3)\n            normal.applyMatrix3(normalMatrix).normalize()\n\n            const uv = _face.uvs[i]\n            uv.fromArray(uvs, arguments[i] * 2)\n          }\n\n          _face.vertexNormalsLength = 3\n\n          _face.material = material\n\n          if (material.vertexColors) {\n            _face.color.fromArray(colors, a * 3)\n          }\n\n          _renderData.elements.push(_face)\n        }\n      }\n\n      return {\n        setObject: setObject,\n        projectVertex: projectVertex,\n        checkTriangleVisibility: checkTriangleVisibility,\n        checkBackfaceCulling: checkBackfaceCulling,\n        pushVertex: pushVertex,\n        pushNormal: pushNormal,\n        pushColor: pushColor,\n        pushUv: pushUv,\n        pushLine: pushLine,\n        pushTriangle: pushTriangle,\n      }\n    }\n\n    const renderList = new RenderList()\n\n    function projectObject(object) {\n      if (object.visible === false) return\n\n      if (object.isLight) {\n        _renderData.lights.push(object)\n      } else if (object.isMesh || object.isLine || object.isPoints) {\n        if (object.material.visible === false) return\n        if (object.frustumCulled === true && _frustum.intersectsObject(object) === false) return\n\n        addObject(object)\n      } else if (object.isSprite) {\n        if (object.material.visible === false) return\n        if (object.frustumCulled === true && _frustum.intersectsSprite(object) === false) return\n\n        addObject(object)\n      }\n\n      const children = object.children\n\n      for (let i = 0, l = children.length; i < l; i++) {\n        projectObject(children[i])\n      }\n    }\n\n    function addObject(object) {\n      _object = getNextObjectInPool()\n      _object.id = object.id\n      _object.object = object\n\n      _vector3.setFromMatrixPosition(object.matrixWorld)\n      _vector3.applyMatrix4(_viewProjectionMatrix)\n      _object.z = _vector3.z\n      _object.renderOrder = object.renderOrder\n\n      _renderData.objects.push(_object)\n    }\n\n    this.projectScene = function (scene, camera, sortObjects, sortElements) {\n      _faceCount = 0\n      _lineCount = 0\n      _spriteCount = 0\n\n      _renderData.elements.length = 0\n\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      _frustum.setFromProjectionMatrix(_viewProjectionMatrix)\n\n      //\n\n      _objectCount = 0\n\n      _renderData.objects.length = 0\n      _renderData.lights.length = 0\n\n      projectObject(scene)\n\n      if (sortObjects === true) {\n        _renderData.objects.sort(painterSort)\n      }\n\n      //\n\n      const objects = _renderData.objects\n\n      for (let o = 0, ol = objects.length; o < ol; o++) {\n        const object = objects[o].object\n        const geometry = object.geometry\n\n        renderList.setObject(object)\n\n        _modelMatrix = object.matrixWorld\n\n        _vertexCount = 0\n\n        if (object.isMesh) {\n          let material = object.material\n\n          const isMultiMaterial = Array.isArray(material)\n\n          const attributes = geometry.attributes\n          const groups = geometry.groups\n\n          if (attributes.position === undefined) continue\n\n          const positions = attributes.position.array\n\n          for (let i = 0, l = positions.length; i < l; i += 3) {\n            let x = positions[i]\n            let y = positions[i + 1]\n            let z = positions[i + 2]\n\n            const morphTargets = geometry.morphAttributes.position\n\n            if (morphTargets !== undefined) {\n              const morphTargetsRelative = geometry.morphTargetsRelative\n              const morphInfluences = object.morphTargetInfluences\n\n              for (let t = 0, tl = morphTargets.length; t < tl; t++) {\n                const influence = morphInfluences[t]\n\n                if (influence === 0) continue\n\n                const target = morphTargets[t]\n\n                if (morphTargetsRelative) {\n                  x += target.getX(i / 3) * influence\n                  y += target.getY(i / 3) * influence\n                  z += target.getZ(i / 3) * influence\n                } else {\n                  x += (target.getX(i / 3) - positions[i]) * influence\n                  y += (target.getY(i / 3) - positions[i + 1]) * influence\n                  z += (target.getZ(i / 3) - positions[i + 2]) * influence\n                }\n              }\n            }\n\n            renderList.pushVertex(x, y, z)\n          }\n\n          if (attributes.normal !== undefined) {\n            const normals = attributes.normal.array\n\n            for (let i = 0, l = normals.length; i < l; i += 3) {\n              renderList.pushNormal(normals[i], normals[i + 1], normals[i + 2])\n            }\n          }\n\n          if (attributes.color !== undefined) {\n            const colors = attributes.color.array\n\n            for (let i = 0, l = colors.length; i < l; i += 3) {\n              renderList.pushColor(colors[i], colors[i + 1], colors[i + 2])\n            }\n          }\n\n          if (attributes.uv !== undefined) {\n            const uvs = attributes.uv.array\n\n            for (let i = 0, l = uvs.length; i < l; i += 2) {\n              renderList.pushUv(uvs[i], uvs[i + 1])\n            }\n          }\n\n          if (geometry.index !== null) {\n            const indices = geometry.index.array\n\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g]\n\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material\n\n                if (material === undefined) continue\n\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material)\n                }\n              }\n            } else {\n              for (let i = 0, l = indices.length; i < l; i += 3) {\n                renderList.pushTriangle(indices[i], indices[i + 1], indices[i + 2], material)\n              }\n            }\n          } else {\n            if (groups.length > 0) {\n              for (let g = 0; g < groups.length; g++) {\n                const group = groups[g]\n\n                material = isMultiMaterial === true ? object.material[group.materialIndex] : object.material\n\n                if (material === undefined) continue\n\n                for (let i = group.start, l = group.start + group.count; i < l; i += 3) {\n                  renderList.pushTriangle(i, i + 1, i + 2, material)\n                }\n              }\n            } else {\n              for (let i = 0, l = positions.length / 3; i < l; i += 3) {\n                renderList.pushTriangle(i, i + 1, i + 2, material)\n              }\n            }\n          }\n        } else if (object.isLine) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix)\n\n          const attributes = geometry.attributes\n\n          if (attributes.position !== undefined) {\n            const positions = attributes.position.array\n\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              renderList.pushVertex(positions[i], positions[i + 1], positions[i + 2])\n            }\n\n            if (attributes.color !== undefined) {\n              const colors = attributes.color.array\n\n              for (let i = 0, l = colors.length; i < l; i += 3) {\n                renderList.pushColor(colors[i], colors[i + 1], colors[i + 2])\n              }\n            }\n\n            if (geometry.index !== null) {\n              const indices = geometry.index.array\n\n              for (let i = 0, l = indices.length; i < l; i += 2) {\n                renderList.pushLine(indices[i], indices[i + 1])\n              }\n            } else {\n              const step = object.isLineSegments ? 2 : 1\n\n              for (let i = 0, l = positions.length / 3 - 1; i < l; i += step) {\n                renderList.pushLine(i, i + 1)\n              }\n            }\n          }\n        } else if (object.isPoints) {\n          _modelViewProjectionMatrix.multiplyMatrices(_viewProjectionMatrix, _modelMatrix)\n\n          const attributes = geometry.attributes\n\n          if (attributes.position !== undefined) {\n            const positions = attributes.position.array\n\n            for (let i = 0, l = positions.length; i < l; i += 3) {\n              _vector4.set(positions[i], positions[i + 1], positions[i + 2], 1)\n              _vector4.applyMatrix4(_modelViewProjectionMatrix)\n\n              pushPoint(_vector4, object, camera)\n            }\n          }\n        } else if (object.isSprite) {\n          object.modelViewMatrix.multiplyMatrices(camera.matrixWorldInverse, object.matrixWorld)\n          _vector4.set(_modelMatrix.elements[12], _modelMatrix.elements[13], _modelMatrix.elements[14], 1)\n          _vector4.applyMatrix4(_viewProjectionMatrix)\n\n          pushPoint(_vector4, object, camera)\n        }\n      }\n\n      if (sortElements === true) {\n        _renderData.elements.sort(painterSort)\n      }\n\n      return _renderData\n    }\n\n    function pushPoint(_vector4, object, camera) {\n      const invW = 1 / _vector4.w\n\n      _vector4.z *= invW\n\n      if (_vector4.z >= -1 && _vector4.z <= 1) {\n        _sprite = getNextSpriteInPool()\n        _sprite.id = object.id\n        _sprite.x = _vector4.x * invW\n        _sprite.y = _vector4.y * invW\n        _sprite.z = _vector4.z\n        _sprite.renderOrder = object.renderOrder\n        _sprite.object = object\n\n        _sprite.rotation = object.rotation\n\n        _sprite.scale.x =\n          object.scale.x *\n          Math.abs(\n            _sprite.x -\n              (_vector4.x + camera.projectionMatrix.elements[0]) / (_vector4.w + camera.projectionMatrix.elements[12]),\n          )\n        _sprite.scale.y =\n          object.scale.y *\n          Math.abs(\n            _sprite.y -\n              (_vector4.y + camera.projectionMatrix.elements[5]) / (_vector4.w + camera.projectionMatrix.elements[13]),\n          )\n\n        _sprite.material = object.material\n\n        _renderData.elements.push(_sprite)\n      }\n    }\n\n    // Pools\n\n    function getNextObjectInPool() {\n      if (_objectCount === _objectPoolLength) {\n        const object = new RenderableObject()\n        _objectPool.push(object)\n        _objectPoolLength++\n        _objectCount++\n        return object\n      }\n\n      return _objectPool[_objectCount++]\n    }\n\n    function getNextVertexInPool() {\n      if (_vertexCount === _vertexPoolLength) {\n        const vertex = new RenderableVertex()\n        _vertexPool.push(vertex)\n        _vertexPoolLength++\n        _vertexCount++\n        return vertex\n      }\n\n      return _vertexPool[_vertexCount++]\n    }\n\n    function getNextFaceInPool() {\n      if (_faceCount === _facePoolLength) {\n        const face = new RenderableFace()\n        _facePool.push(face)\n        _facePoolLength++\n        _faceCount++\n        return face\n      }\n\n      return _facePool[_faceCount++]\n    }\n\n    function getNextLineInPool() {\n      if (_lineCount === _linePoolLength) {\n        const line = new RenderableLine()\n        _linePool.push(line)\n        _linePoolLength++\n        _lineCount++\n        return line\n      }\n\n      return _linePool[_lineCount++]\n    }\n\n    function getNextSpriteInPool() {\n      if (_spriteCount === _spritePoolLength) {\n        const sprite = new RenderableSprite()\n        _spritePool.push(sprite)\n        _spritePoolLength++\n        _spriteCount++\n        return sprite\n      }\n\n      return _spritePool[_spriteCount++]\n    }\n\n    //\n\n    function painterSort(a, b) {\n      if (a.renderOrder !== b.renderOrder) {\n        return a.renderOrder - b.renderOrder\n      } else if (a.z !== b.z) {\n        return b.z - a.z\n      } else if (a.id !== b.id) {\n        return a.id - b.id\n      } else {\n        return 0\n      }\n    }\n\n    function clipLine(s1, s2) {\n      let alpha1 = 0,\n        alpha2 = 1\n\n      // Calculate the boundary coordinate of each vertex for the near and far clip planes,\n      // Z = -1 and Z = +1, respectively.\n\n      const bc1near = s1.z + s1.w,\n        bc2near = s2.z + s2.w,\n        bc1far = -s1.z + s1.w,\n        bc2far = -s2.z + s2.w\n\n      if (bc1near >= 0 && bc2near >= 0 && bc1far >= 0 && bc2far >= 0) {\n        // Both vertices lie entirely within all clip planes.\n        return true\n      } else if ((bc1near < 0 && bc2near < 0) || (bc1far < 0 && bc2far < 0)) {\n        // Both vertices lie entirely outside one of the clip planes.\n        return false\n      } else {\n        // The line segment spans at least one clip plane.\n\n        if (bc1near < 0) {\n          // v1 lies outside the near plane, v2 inside\n          alpha1 = Math.max(alpha1, bc1near / (bc1near - bc2near))\n        } else if (bc2near < 0) {\n          // v2 lies outside the near plane, v1 inside\n          alpha2 = Math.min(alpha2, bc1near / (bc1near - bc2near))\n        }\n\n        if (bc1far < 0) {\n          // v1 lies outside the far plane, v2 inside\n          alpha1 = Math.max(alpha1, bc1far / (bc1far - bc2far))\n        } else if (bc2far < 0) {\n          // v2 lies outside the far plane, v2 inside\n          alpha2 = Math.min(alpha2, bc1far / (bc1far - bc2far))\n        }\n\n        if (alpha2 < alpha1) {\n          // The line segment spans two boundaries, but is outside both of them.\n          // (This can't happen when we're only clipping against just near/far but good\n          //  to leave the check here for future usage if other clip planes are added.)\n          return false\n        } else {\n          // Update the s1 and s2 vertices to match the clipped line segment.\n          s1.lerp(s2, alpha1)\n          s2.lerp(s1, 1 - alpha2)\n\n          return true\n        }\n      }\n    }\n  }\n}\n\nexport { RenderableObject, RenderableFace, RenderableVertex, RenderableLine, RenderableSprite, Projector }\n"], "names": ["_vector4"], "mappings": ";AAEA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,KAAK;AAEV,SAAK,SAAS;AACd,SAAK,IAAI;AACT,SAAK,cAAc;AAAA,EACpB;AACH;AAIA,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,KAAK;AAEV,SAAK,KAAK,IAAI,iBAAkB;AAChC,SAAK,KAAK,IAAI,iBAAkB;AAChC,SAAK,KAAK,IAAI,iBAAkB;AAEhC,SAAK,cAAc,IAAI,QAAS;AAEhC,SAAK,qBAAqB,CAAC,IAAI,QAAO,GAAI,IAAI,QAAS,GAAE,IAAI,SAAS;AACtE,SAAK,sBAAsB;AAE3B,SAAK,QAAQ,IAAI,MAAO;AACxB,SAAK,WAAW;AAChB,SAAK,MAAM,CAAC,IAAI,QAAO,GAAI,IAAI,QAAS,GAAE,IAAI,SAAS;AAEvD,SAAK,IAAI;AACT,SAAK,cAAc;AAAA,EACpB;AACH;AAIA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,WAAW,IAAI,QAAS;AAC7B,SAAK,gBAAgB,IAAI,QAAS;AAClC,SAAK,iBAAiB,IAAI,QAAS;AAEnC,SAAK,UAAU;AAAA,EAChB;AAAA,EAED,KAAK,QAAQ;AACX,SAAK,cAAc,KAAK,OAAO,aAAa;AAC5C,SAAK,eAAe,KAAK,OAAO,cAAc;AAAA,EAC/C;AACH;AAIA,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,KAAK;AAEV,SAAK,KAAK,IAAI,iBAAkB;AAChC,SAAK,KAAK,IAAI,iBAAkB;AAEhC,SAAK,eAAe,CAAC,IAAI,MAAK,GAAI,IAAI,MAAK,CAAE;AAC7C,SAAK,WAAW;AAEhB,SAAK,IAAI;AACT,SAAK,cAAc;AAAA,EACpB;AACH;AAIA,MAAM,iBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,KAAK;AAEV,SAAK,SAAS;AAEd,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AAET,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,QAAS;AAE1B,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACpB;AACH;AAIA,MAAM,UAAU;AAAA,EACd,cAAc;AACZ,QAAI,SACF,cACA,oBAAoB,GACpB,SACA,cACA,oBAAoB,GACpB,OACA,YACA,kBAAkB,GAClB,OACA,YACA,kBAAkB,GAClB,SACA,cACA,oBAAoB,GACpB;AAEF,UAAM,cAAc,EAAE,SAAS,CAAA,GAAI,QAAQ,CAAE,GAAE,UAAU,GAAI,GAC3D,WAAW,IAAI,QAAS,GACxB,WAAW,IAAI,QAAS,GACxB,WAAW,IAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC,GACjE,eAAe,IAAI,KAAM,GACzB,WAAW,IAAI,MAAM,CAAC,GACtB,cAAc,IAAI,QAAS,GAC3B,wBAAwB,IAAI,QAAS,GACrC,6BAA6B,IAAI,QAAS,GAC1C,WAAW,IAAI,QAAS,GACxB,cAAc,CAAE,GAChB,cAAc,CAAE,GAChB,YAAY,CAAE,GACd,YAAY,CAAE,GACd,cAAc,CAAE;AAIlB,aAAS,aAAa;AACpB,YAAM,UAAU,CAAE;AAClB,YAAM,SAAS,CAAE;AACjB,YAAM,MAAM,CAAE;AAEd,UAAI,SAAS;AAEb,YAAM,eAAe,IAAI,QAAS;AAElC,eAAS,UAAU,OAAO;AACxB,iBAAS;AAET,qBAAa,gBAAgB,OAAO,WAAW;AAE/C,gBAAQ,SAAS;AACjB,eAAO,SAAS;AAChB,YAAI,SAAS;AAAA,MACd;AAED,eAAS,cAAc,QAAQ;AAC7B,cAAM,WAAW,OAAO;AACxB,cAAM,gBAAgB,OAAO;AAC7B,cAAM,iBAAiB,OAAO;AAE9B,sBAAc,KAAK,QAAQ,EAAE,aAAa,YAAY;AACtD,uBAAe,KAAK,aAAa,EAAE,aAAa,qBAAqB;AAErE,cAAM,OAAO,IAAI,eAAe;AAEhC,uBAAe,KAAK;AACpB,uBAAe,KAAK;AACpB,uBAAe,KAAK;AAEpB,eAAO,UACL,eAAe,KAAK,MACpB,eAAe,KAAK,KACpB,eAAe,KAAK,MACpB,eAAe,KAAK,KACpB,eAAe,KAAK,MACpB,eAAe,KAAK;AAAA,MACvB;AAED,eAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,kBAAU,oBAAqB;AAC/B,gBAAQ,SAAS,IAAI,GAAG,GAAG,CAAC;AAE5B,sBAAc,OAAO;AAAA,MACtB;AAED,eAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,gBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,MACrB;AAED,eAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,eAAO,KAAK,GAAG,GAAG,CAAC;AAAA,MACpB;AAED,eAAS,OAAO,GAAG,GAAG;AACpB,YAAI,KAAK,GAAG,CAAC;AAAA,MACd;AAED,eAAS,wBAAwB,IAAI,IAAI,IAAI;AAC3C,YAAI,GAAG,YAAY,QAAQ,GAAG,YAAY,QAAQ,GAAG,YAAY;AAAM,iBAAO;AAE9E,iBAAS,CAAC,IAAI,GAAG;AACjB,iBAAS,CAAC,IAAI,GAAG;AACjB,iBAAS,CAAC,IAAI,GAAG;AAEjB,eAAO,SAAS,cAAc,aAAa,cAAc,QAAQ,CAAC;AAAA,MACnE;AAED,eAAS,qBAAqB,IAAI,IAAI,IAAI;AACxC,gBACG,GAAG,eAAe,IAAI,GAAG,eAAe,MAAM,GAAG,eAAe,IAAI,GAAG,eAAe,MACpF,GAAG,eAAe,IAAI,GAAG,eAAe,MAAM,GAAG,eAAe,IAAI,GAAG,eAAe,KACzF;AAAA,MAEH;AAED,eAAS,SAAS,GAAG,GAAG;AACtB,cAAM,KAAK,YAAY,CAAC;AACxB,cAAM,KAAK,YAAY,CAAC;AAIxB,WAAG,eAAe,KAAK,GAAG,QAAQ,EAAE,aAAa,0BAA0B;AAC3E,WAAG,eAAe,KAAK,GAAG,QAAQ,EAAE,aAAa,0BAA0B;AAE3E,YAAI,SAAS,GAAG,gBAAgB,GAAG,cAAc,MAAM,MAAM;AAE3D,aAAG,eAAe,eAAe,IAAI,GAAG,eAAe,CAAC;AACxD,aAAG,eAAe,eAAe,IAAI,GAAG,eAAe,CAAC;AAExD,kBAAQ,kBAAmB;AAC3B,gBAAM,KAAK,OAAO;AAClB,gBAAM,GAAG,KAAK,EAAE;AAChB,gBAAM,GAAG,KAAK,EAAE;AAChB,gBAAM,IAAI,KAAK,IAAI,GAAG,eAAe,GAAG,GAAG,eAAe,CAAC;AAC3D,gBAAM,cAAc,OAAO;AAE3B,gBAAM,WAAW,OAAO;AAExB,cAAI,OAAO,SAAS,cAAc;AAChC,kBAAM,aAAa,CAAC,EAAE,UAAU,QAAQ,IAAI,CAAC;AAC7C,kBAAM,aAAa,CAAC,EAAE,UAAU,QAAQ,IAAI,CAAC;AAAA,UAC9C;AAED,sBAAY,SAAS,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAED,eAAS,aAAa,GAAG,GAAG,GAAG,UAAU;AACvC,cAAM,KAAK,YAAY,CAAC;AACxB,cAAM,KAAK,YAAY,CAAC;AACxB,cAAM,KAAK,YAAY,CAAC;AAExB,YAAI,wBAAwB,IAAI,IAAI,EAAE,MAAM;AAAO;AAEnD,YAAI,SAAS,SAAS,cAAc,qBAAqB,IAAI,IAAI,EAAE,MAAM,MAAM;AAC7E,kBAAQ,kBAAmB;AAE3B,gBAAM,KAAK,OAAO;AAClB,gBAAM,GAAG,KAAK,EAAE;AAChB,gBAAM,GAAG,KAAK,EAAE;AAChB,gBAAM,GAAG,KAAK,EAAE;AAChB,gBAAM,KAAK,GAAG,eAAe,IAAI,GAAG,eAAe,IAAI,GAAG,eAAe,KAAK;AAC9E,gBAAM,cAAc,OAAO;AAG3B,mBAAS,WAAW,GAAG,UAAU,GAAG,QAAQ;AAC5C,mBAAS,WAAW,GAAG,UAAU,GAAG,QAAQ;AAC5C,mBAAS,MAAM,QAAQ;AACvB,gBAAM,YAAY,KAAK,QAAQ;AAC/B,gBAAM,YAAY,aAAa,YAAY,EAAE,UAAW;AAExD,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,SAAS,MAAM,mBAAmB,CAAC;AACzC,mBAAO,UAAU,SAAS,UAAU,CAAC,IAAI,CAAC;AAC1C,mBAAO,aAAa,YAAY,EAAE,UAAW;AAE7C,kBAAM,KAAK,MAAM,IAAI,CAAC;AACtB,eAAG,UAAU,KAAK,UAAU,CAAC,IAAI,CAAC;AAAA,UACnC;AAED,gBAAM,sBAAsB;AAE5B,gBAAM,WAAW;AAEjB,cAAI,SAAS,cAAc;AACzB,kBAAM,MAAM,UAAU,QAAQ,IAAI,CAAC;AAAA,UACpC;AAED,sBAAY,SAAS,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAED,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAED,UAAM,aAAa,IAAI,WAAY;AAEnC,aAAS,cAAc,QAAQ;AAC7B,UAAI,OAAO,YAAY;AAAO;AAE9B,UAAI,OAAO,SAAS;AAClB,oBAAY,OAAO,KAAK,MAAM;AAAA,MACtC,WAAiB,OAAO,UAAU,OAAO,UAAU,OAAO,UAAU;AAC5D,YAAI,OAAO,SAAS,YAAY;AAAO;AACvC,YAAI,OAAO,kBAAkB,QAAQ,SAAS,iBAAiB,MAAM,MAAM;AAAO;AAElF,kBAAU,MAAM;AAAA,MACxB,WAAiB,OAAO,UAAU;AAC1B,YAAI,OAAO,SAAS,YAAY;AAAO;AACvC,YAAI,OAAO,kBAAkB,QAAQ,SAAS,iBAAiB,MAAM,MAAM;AAAO;AAElF,kBAAU,MAAM;AAAA,MACjB;AAED,YAAM,WAAW,OAAO;AAExB,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,sBAAc,SAAS,CAAC,CAAC;AAAA,MAC1B;AAAA,IACF;AAED,aAAS,UAAU,QAAQ;AACzB,gBAAU,oBAAqB;AAC/B,cAAQ,KAAK,OAAO;AACpB,cAAQ,SAAS;AAEjB,eAAS,sBAAsB,OAAO,WAAW;AACjD,eAAS,aAAa,qBAAqB;AAC3C,cAAQ,IAAI,SAAS;AACrB,cAAQ,cAAc,OAAO;AAE7B,kBAAY,QAAQ,KAAK,OAAO;AAAA,IACjC;AAED,SAAK,eAAe,SAAU,OAAO,QAAQ,aAAa,cAAc;AACtE,mBAAa;AACb,mBAAa;AACb,qBAAe;AAEf,kBAAY,SAAS,SAAS;AAE9B,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AACnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,kBAAY,KAAK,OAAO,kBAAkB;AAC1C,4BAAsB,iBAAiB,OAAO,kBAAkB,WAAW;AAE3E,eAAS,wBAAwB,qBAAqB;AAItD,qBAAe;AAEf,kBAAY,QAAQ,SAAS;AAC7B,kBAAY,OAAO,SAAS;AAE5B,oBAAc,KAAK;AAEnB,UAAI,gBAAgB,MAAM;AACxB,oBAAY,QAAQ,KAAK,WAAW;AAAA,MACrC;AAID,YAAM,UAAU,YAAY;AAE5B,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAChD,cAAM,SAAS,QAAQ,CAAC,EAAE;AAC1B,cAAM,WAAW,OAAO;AAExB,mBAAW,UAAU,MAAM;AAE3B,uBAAe,OAAO;AAEtB,uBAAe;AAEf,YAAI,OAAO,QAAQ;AACjB,cAAI,WAAW,OAAO;AAEtB,gBAAM,kBAAkB,MAAM,QAAQ,QAAQ;AAE9C,gBAAM,aAAa,SAAS;AAC5B,gBAAM,SAAS,SAAS;AAExB,cAAI,WAAW,aAAa;AAAW;AAEvC,gBAAM,YAAY,WAAW,SAAS;AAEtC,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACnD,gBAAI,IAAI,UAAU,CAAC;AACnB,gBAAI,IAAI,UAAU,IAAI,CAAC;AACvB,gBAAI,IAAI,UAAU,IAAI,CAAC;AAEvB,kBAAM,eAAe,SAAS,gBAAgB;AAE9C,gBAAI,iBAAiB,QAAW;AAC9B,oBAAM,uBAAuB,SAAS;AACtC,oBAAM,kBAAkB,OAAO;AAE/B,uBAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK;AACrD,sBAAM,YAAY,gBAAgB,CAAC;AAEnC,oBAAI,cAAc;AAAG;AAErB,sBAAM,SAAS,aAAa,CAAC;AAE7B,oBAAI,sBAAsB;AACxB,uBAAK,OAAO,KAAK,IAAI,CAAC,IAAI;AAC1B,uBAAK,OAAO,KAAK,IAAI,CAAC,IAAI;AAC1B,uBAAK,OAAO,KAAK,IAAI,CAAC,IAAI;AAAA,gBAC5C,OAAuB;AACL,wBAAM,OAAO,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK;AAC3C,wBAAM,OAAO,KAAK,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,KAAK;AAC/C,wBAAM,OAAO,KAAK,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,KAAK;AAAA,gBAChD;AAAA,cACF;AAAA,YACF;AAED,uBAAW,WAAW,GAAG,GAAG,CAAC;AAAA,UAC9B;AAED,cAAI,WAAW,WAAW,QAAW;AACnC,kBAAM,UAAU,WAAW,OAAO;AAElC,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,GAAG;AACjD,yBAAW,WAAW,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;AAAA,YACjE;AAAA,UACF;AAED,cAAI,WAAW,UAAU,QAAW;AAClC,kBAAM,SAAS,WAAW,MAAM;AAEhC,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,GAAG;AAChD,yBAAW,UAAU,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,YAC7D;AAAA,UACF;AAED,cAAI,WAAW,OAAO,QAAW;AAC/B,kBAAM,MAAM,WAAW,GAAG;AAE1B,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC7C,yBAAW,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AAAA,YACrC;AAAA,UACF;AAED,cAAI,SAAS,UAAU,MAAM;AAC3B,kBAAM,UAAU,SAAS,MAAM;AAE/B,gBAAI,OAAO,SAAS,GAAG;AACrB,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,sBAAM,QAAQ,OAAO,CAAC;AAEtB,2BAAW,oBAAoB,OAAO,OAAO,SAAS,MAAM,aAAa,IAAI,OAAO;AAEpF,oBAAI,aAAa;AAAW;AAE5B,yBAAS,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ,MAAM,OAAO,IAAI,GAAG,KAAK,GAAG;AACtE,6BAAW,aAAa,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ;AAAA,gBAC7E;AAAA,cACF;AAAA,YACf,OAAmB;AACL,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,GAAG;AACjD,2BAAW,aAAa,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ;AAAA,cAC7E;AAAA,YACF;AAAA,UACb,OAAiB;AACL,gBAAI,OAAO,SAAS,GAAG;AACrB,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,sBAAM,QAAQ,OAAO,CAAC;AAEtB,2BAAW,oBAAoB,OAAO,OAAO,SAAS,MAAM,aAAa,IAAI,OAAO;AAEpF,oBAAI,aAAa;AAAW;AAE5B,yBAAS,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ,MAAM,OAAO,IAAI,GAAG,KAAK,GAAG;AACtE,6BAAW,aAAa,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ;AAAA,gBAClD;AAAA,cACF;AAAA,YACf,OAAmB;AACL,uBAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,IAAI,GAAG,KAAK,GAAG;AACvD,2BAAW,aAAa,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ;AAAA,cAClD;AAAA,YACF;AAAA,UACF;AAAA,QACX,WAAmB,OAAO,QAAQ;AACxB,qCAA2B,iBAAiB,uBAAuB,YAAY;AAE/E,gBAAM,aAAa,SAAS;AAE5B,cAAI,WAAW,aAAa,QAAW;AACrC,kBAAM,YAAY,WAAW,SAAS;AAEtC,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACnD,yBAAW,WAAW,UAAU,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;AAAA,YACvE;AAED,gBAAI,WAAW,UAAU,QAAW;AAClC,oBAAM,SAAS,WAAW,MAAM;AAEhC,uBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,GAAG;AAChD,2BAAW,UAAU,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,cAC7D;AAAA,YACF;AAED,gBAAI,SAAS,UAAU,MAAM;AAC3B,oBAAM,UAAU,SAAS,MAAM;AAE/B,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,GAAG;AACjD,2BAAW,SAAS,QAAQ,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC;AAAA,cAC/C;AAAA,YACf,OAAmB;AACL,oBAAM,OAAO,OAAO,iBAAiB,IAAI;AAEzC,uBAAS,IAAI,GAAG,IAAI,UAAU,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK,MAAM;AAC9D,2BAAW,SAAS,GAAG,IAAI,CAAC;AAAA,cAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACX,WAAmB,OAAO,UAAU;AAC1B,qCAA2B,iBAAiB,uBAAuB,YAAY;AAE/E,gBAAM,aAAa,SAAS;AAE5B,cAAI,WAAW,aAAa,QAAW;AACrC,kBAAM,YAAY,WAAW,SAAS;AAEtC,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACnD,uBAAS,IAAI,UAAU,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,CAAC;AAChE,uBAAS,aAAa,0BAA0B;AAEhD,wBAAU,UAAU,QAAQ,MAAM;AAAA,YACnC;AAAA,UACF;AAAA,QACX,WAAmB,OAAO,UAAU;AAC1B,iBAAO,gBAAgB,iBAAiB,OAAO,oBAAoB,OAAO,WAAW;AACrF,mBAAS,IAAI,aAAa,SAAS,EAAE,GAAG,aAAa,SAAS,EAAE,GAAG,aAAa,SAAS,EAAE,GAAG,CAAC;AAC/F,mBAAS,aAAa,qBAAqB;AAE3C,oBAAU,UAAU,QAAQ,MAAM;AAAA,QACnC;AAAA,MACF;AAED,UAAI,iBAAiB,MAAM;AACzB,oBAAY,SAAS,KAAK,WAAW;AAAA,MACtC;AAED,aAAO;AAAA,IACR;AAED,aAAS,UAAUA,WAAU,QAAQ,QAAQ;AAC3C,YAAM,OAAO,IAAIA,UAAS;AAE1B,MAAAA,UAAS,KAAK;AAEd,UAAIA,UAAS,KAAK,MAAMA,UAAS,KAAK,GAAG;AACvC,kBAAU,oBAAqB;AAC/B,gBAAQ,KAAK,OAAO;AACpB,gBAAQ,IAAIA,UAAS,IAAI;AACzB,gBAAQ,IAAIA,UAAS,IAAI;AACzB,gBAAQ,IAAIA,UAAS;AACrB,gBAAQ,cAAc,OAAO;AAC7B,gBAAQ,SAAS;AAEjB,gBAAQ,WAAW,OAAO;AAE1B,gBAAQ,MAAM,IACZ,OAAO,MAAM,IACb,KAAK;AAAA,UACH,QAAQ,KACLA,UAAS,IAAI,OAAO,iBAAiB,SAAS,CAAC,MAAMA,UAAS,IAAI,OAAO,iBAAiB,SAAS,EAAE;AAAA,QACzG;AACH,gBAAQ,MAAM,IACZ,OAAO,MAAM,IACb,KAAK;AAAA,UACH,QAAQ,KACLA,UAAS,IAAI,OAAO,iBAAiB,SAAS,CAAC,MAAMA,UAAS,IAAI,OAAO,iBAAiB,SAAS,EAAE;AAAA,QACzG;AAEH,gBAAQ,WAAW,OAAO;AAE1B,oBAAY,SAAS,KAAK,OAAO;AAAA,MAClC;AAAA,IACF;AAID,aAAS,sBAAsB;AAC7B,UAAI,iBAAiB,mBAAmB;AACtC,cAAM,SAAS,IAAI,iBAAkB;AACrC,oBAAY,KAAK,MAAM;AACvB;AACA;AACA,eAAO;AAAA,MACR;AAED,aAAO,YAAY,cAAc;AAAA,IAClC;AAED,aAAS,sBAAsB;AAC7B,UAAI,iBAAiB,mBAAmB;AACtC,cAAM,SAAS,IAAI,iBAAkB;AACrC,oBAAY,KAAK,MAAM;AACvB;AACA;AACA,eAAO;AAAA,MACR;AAED,aAAO,YAAY,cAAc;AAAA,IAClC;AAED,aAAS,oBAAoB;AAC3B,UAAI,eAAe,iBAAiB;AAClC,cAAM,OAAO,IAAI,eAAgB;AACjC,kBAAU,KAAK,IAAI;AACnB;AACA;AACA,eAAO;AAAA,MACR;AAED,aAAO,UAAU,YAAY;AAAA,IAC9B;AAED,aAAS,oBAAoB;AAC3B,UAAI,eAAe,iBAAiB;AAClC,cAAM,OAAO,IAAI,eAAgB;AACjC,kBAAU,KAAK,IAAI;AACnB;AACA;AACA,eAAO;AAAA,MACR;AAED,aAAO,UAAU,YAAY;AAAA,IAC9B;AAED,aAAS,sBAAsB;AAC7B,UAAI,iBAAiB,mBAAmB;AACtC,cAAM,SAAS,IAAI,iBAAkB;AACrC,oBAAY,KAAK,MAAM;AACvB;AACA;AACA,eAAO;AAAA,MACR;AAED,aAAO,YAAY,cAAc;AAAA,IAClC;AAID,aAAS,YAAY,GAAG,GAAG;AACzB,UAAI,EAAE,gBAAgB,EAAE,aAAa;AACnC,eAAO,EAAE,cAAc,EAAE;AAAA,MAC1B,WAAU,EAAE,MAAM,EAAE,GAAG;AACtB,eAAO,EAAE,IAAI,EAAE;AAAA,MAChB,WAAU,EAAE,OAAO,EAAE,IAAI;AACxB,eAAO,EAAE,KAAK,EAAE;AAAA,MACxB,OAAa;AACL,eAAO;AAAA,MACR;AAAA,IACF;AAED,aAAS,SAAS,IAAI,IAAI;AACxB,UAAI,SAAS,GACX,SAAS;AAKX,YAAM,UAAU,GAAG,IAAI,GAAG,GACxB,UAAU,GAAG,IAAI,GAAG,GACpB,SAAS,CAAC,GAAG,IAAI,GAAG,GACpB,SAAS,CAAC,GAAG,IAAI,GAAG;AAEtB,UAAI,WAAW,KAAK,WAAW,KAAK,UAAU,KAAK,UAAU,GAAG;AAE9D,eAAO;AAAA,MACf,WAAkB,UAAU,KAAK,UAAU,KAAO,SAAS,KAAK,SAAS,GAAI;AAErE,eAAO;AAAA,MACf,OAAa;AAGL,YAAI,UAAU,GAAG;AAEf,mBAAS,KAAK,IAAI,QAAQ,WAAW,UAAU,QAAQ;AAAA,QACjE,WAAmB,UAAU,GAAG;AAEtB,mBAAS,KAAK,IAAI,QAAQ,WAAW,UAAU,QAAQ;AAAA,QACxD;AAED,YAAI,SAAS,GAAG;AAEd,mBAAS,KAAK,IAAI,QAAQ,UAAU,SAAS,OAAO;AAAA,QAC9D,WAAmB,SAAS,GAAG;AAErB,mBAAS,KAAK,IAAI,QAAQ,UAAU,SAAS,OAAO;AAAA,QACrD;AAED,YAAI,SAAS,QAAQ;AAInB,iBAAO;AAAA,QACjB,OAAe;AAEL,aAAG,KAAK,IAAI,MAAM;AAClB,aAAG,KAAK,IAAI,IAAI,MAAM;AAEtB,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACH;"}