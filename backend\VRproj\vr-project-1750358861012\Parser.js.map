{"version":3,"file":"parser.js","sourceRoot":"","sources":["../src/parser.ts"],"names":[],"mappings":";;;;;AA8LS,sBAAK;AAAE,wCAAc;AAjL9B,oEAA2D;AAC3D,4EAAgF;AAChF,kEAA8D;AAC9D,kDAA0B;AAC1B,2CAA0C;AAE1C,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,iCAAiC,CAAC,CAAC;AAerD,SAAS,eAAe,CACtB,KAA0B,EAC1B,QAAQ,GAAG,KAAK;IAEhB,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;QAC/B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,kBAAkB,GAAG,wBAAwB,CAAC;AACpD,SAAS,MAAM,CAAC,eAAmC;IACjD,IAAI,eAAe,CAAC,GAAG,EAAE,CAAC;QACxB,OAAO,eAAe,CAAC,GAAG;aACvB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC3D,MAAM,CAAC,CAAC,GAAG,EAAc,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,IAAI,yBAAY,CAAC,GAAG,CAAC;IAC1D,gIAAgI;IAChI,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB,KAAK,yBAAY,CAAC,MAAM;YACtB,OAAO,CAAC,aAAa,CAAC,CAAC;QACzB;YACE,OAAO,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,KAAK,CACZ,IAA4B,EAC5B,OAAuB;IAEvB,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC;AAC3C,CAAC;AAED,SAAS,cAAc,CACrB,IAA4B,EAC5B,aAAoC;IAEpC,IAAI,CAAC,aAAa,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;QACxD,aAAa,GAAG,EAAE,CAAC;IACrB,CAAC;SAAM,CAAC;QACN,aAAa,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;IACvC,CAAC;IACD,2EAA2E;IAC3E,yFAAyF;IACzF,IACE,aAAa,CAAC,UAAU,KAAK,QAAQ;QACrC,aAAa,CAAC,UAAU,KAAK,QAAQ,EACrC,CAAC;QACD,aAAa,CAAC,UAAU,GAAG,QAAQ,CAAC;IACtC,CAAC;IACD,IAAI,OAAO,aAAa,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;QACnD,aAAa,CAAC,YAAY,GAAG,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,MAAM,kCAAkC,GAAG,eAAe,CACxD,aAAa,CAAC,kCAAkC,EAChD,IAAI,CACL,CAAC;IAEF,MAAM,eAAe,GAAG;QACtB,GAAG,EAAE,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC;QACpD,GAAG,CAAC,CAAC,kCAAkC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QAC/D,GAAG,aAAa;QAChB,2GAA2G;QAC3G,6FAA6F;QAC7F,2CAA2C,EAAE,KAAK;QAClD,wEAAwE;QACxE,qEAAqE;QACrE,OAAO,EAAE,IAAI;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,IAAI;KACa,CAAC;IAE5B,MAAM,cAAc,GAAmB;QACrC,YAAY,EAAE,aAAa,CAAC,YAAY,CAAC,YAAY;QACrD,eAAe,EAAE,aAAa,CAAC,eAAe;QAC9C,SAAS,EAAE,aAAa,CAAC,SAAS;QAClC,GAAG,EAAE,aAAa,CAAC,GAAG;QACtB,UAAU,EAAE,aAAa,CAAC,UAAU;KACrC,CAAC;IAEF,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,IAAA,4CAAwB,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IAC1E,GAAG,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;IAE1C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;QACrB,6DAA6D;QAC7D,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,IAAI,cAAc,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YAC/B,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;YAC7C,GAAG,CAAC,gCAAgC,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC;QACD;QACE,oEAAoE;QACpE,cAAc,CAAC,SAAS,KAAK,SAAS;YACtC,eAAe,CAAC,UAAU,IAAI,IAAI,EAClC,CAAC;YACD,2DAA2D;YAC3D,MAAM,OAAO,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChE,cAAc,CAAC,SAAS,GAAG,OAAO,CAAC;YACnC,GAAG,CAAC,qCAAqC,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;QACvE,CAAC;QACD;QACE,oEAAoE;QACpE,cAAc,CAAC,eAAe,KAAK,SAAS;YAC5C,eAAe,CAAC,kBAAkB,IAAI,IAAI,EAC1C,CAAC;YACD,kEAAkE;YAClE,MAAM,WAAW,GAAG,eAAe,CAAC,kBAAkB;iBACnD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBACb,IAAI,EAAE,CAAC;YACV,cAAc,CAAC,eAAe,GAAG,WAAW,CAAC;YAC7C,GAAG,CACD,2CAA2C,EAC3C,cAAc,CAAC,eAAe,CAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAAG,IAAA,uBAAO,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IAElD,mDAAmD;IACnD,QAAQ,CAAC,qBAAqB;QAC5B,aAAa,CAAC,qBAAqB,KAAK,IAAI,CAAC;IAC/C,QAAQ,CAAC,sBAAsB;QAC7B,aAAa,CAAC,sBAAsB,KAAK,IAAI,CAAC;IAEhD,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAX,0BAAW,EAAE,CAAC;AACtD,CAAC"}AK,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,GAAG,EAAE,MAAgB;QACrB,QAAQ,EAAE,IAAA,2CAAoB,EAAC,OAAO,EAAE,OAAO,CAAC;KACjD,CAAC;AACJ,CAAC"}