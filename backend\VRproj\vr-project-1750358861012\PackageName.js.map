{"version": 3, "file": "PackageName.js", "sourceRoot": "", "sources": ["../src/PackageName.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AA+D3D;;;;;;;;GAQG;AACH,MAAa,iBAAiB;IAO5B,YAAmB,UAAqC,EAAE;QACxD,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;IACjC,CAAC;IAED;;;;;;;;OAQG;IACI,QAAQ,CAAC,WAAmB;QACjC,MAAM,MAAM,GAA8B;YACxC,KAAK,EAAE,EAAE;YACT,YAAY,EAAE,EAAE;YAChB,KAAK,EAAE,EAAE;SACV,CAAC;QAEF,IAAI,KAAK,GAAW,WAAW,CAAC;QAEhC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,CAAC,KAAK,GAAG,gDAAgD,CAAC;YAChE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,uBAAuB;QACvB,wGAAwG;QACxG,IAAI,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC7B,mDAAmD;YACnD,MAAM,CAAC,KAAK,GAAG,uDAAuD,CAAC;YACvE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACrB,MAAM,iBAAiB,GAAW,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACrB,MAAM,CAAC,KAAK,GAAG,kBAAkB,WAAW,0CAA0C,CAAC;gBACvF,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,8BAA8B;YAC9B,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;YAElD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;QAE5B,IAAI,MAAM,CAAC,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,MAAM,CAAC,KAAK,GAAG,kBAAkB,WAAW,mCAAmC,CAAC;YAChF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,KAAK,EAAE,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,GAAG,oCAAoC,CAAC;YACpD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,uBAAuB;QACvB,sDAAsD;QACtD,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACrE,MAAM,CAAC,KAAK,GAAG,qBAAqB,WAAW,oCAAoC,CAAC;YACpF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,0DAA0D;QAC1D,MAAM,uBAAuB,GAC3B,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;QAExE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAClC,8DAA8D;YAC9D,+EAA+E;YAC/E,gDAAgD;YAChD,uEAAuE;YACvE,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBAChD,MAAM,CAAC,KAAK,GAAG,sBAAsB,MAAM,CAAC,KAAK,0CAA0C,CAAC;gBAC5F,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,6FAA6F;QAC7F,iEAAiE;QACjE,MAAM,KAAK,GAA4B,uBAAuB,CAAC,KAAK,CAClE,iBAAiB,CAAC,4BAA4B,CAC/C,CAAC;QACF,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,CAAC,KAAK,GAAG,qBAAqB,WAAW,qCAAqC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;YAChG,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,WAAmB;QAC9B,MAAM,MAAM,GAA8B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACrE,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,WAAmB;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,WAAmB;QACxC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,WAAmB;QACpC,MAAM,MAAM,GAA8B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QACrE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,WAAmB;QACjC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,KAAa,EAAE,YAAoB;QACrD,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QACD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,MAAc,CAAC;QACnB,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjB,MAAM,GAAG,YAAY,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC;QACtC,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEtB,OAAO,MAAM,CAAC;IAChB,CAAC;;AAvLH,8CAwLC;AAvLC,qFAAqF;AACrF,qFAAqF;AAC7D,8CAA4B,GAAW,mBAAmB,CAAC;AAuLrF;;;;;;;;;GASG;AACH,MAAa,WAAW;IAGtB,+CAA+C;IACxC,MAAM,CAAC,QAAQ,CAAC,WAAmB;QACxC,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,4CAA4C;IACrC,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC;IAED,+CAA+C;IACxC,MAAM,CAAC,QAAQ,CAAC,WAAmB;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED,sDAAsD;IAC/C,MAAM,CAAC,eAAe,CAAC,WAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,kDAAkD;IAC3C,MAAM,CAAC,WAAW,CAAC,WAAmB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED,+CAA+C;IACxC,MAAM,CAAC,QAAQ,CAAC,WAAmB;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED,mDAAmD;IAC5C,MAAM,CAAC,YAAY,CAAC,KAAa,EAAE,YAAoB;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACxD,CAAC;;AApCH,kCAqCC;AApCyB,mBAAO,GAAsB,IAAI,iBAAiB,EAAE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON>IC<PERSON><PERSON> in the project root for license information.\n\n/**\n * A package name that has been separated into its scope and unscoped name.\n *\n * @public\n */\nexport interface IParsedPackageName {\n  /**\n   * The parsed NPM scope, or an empty string if there was no scope.  The scope value will\n   * always include the at-sign.\n   * @remarks\n   * For example, if the parsed input was \"\\@scope/example\", then scope would be \"\\@scope\".\n   */\n  scope: string;\n\n  /**\n   * The parsed NPM package name without the scope.\n   * @remarks\n   * For example, if the parsed input was \"\\@scope/example\", then the name would be \"example\".\n   */\n  unscopedName: string;\n}\n\n/**\n * Result object returned by {@link PackageName.tryParse}\n *\n * @public\n */\nexport interface IParsedPackageNameOrError extends IParsedPackageName {\n  /**\n   * If the input string could not be parsed, then this string will contain a nonempty\n   * error message.  Otherwise it will be an empty string.\n   */\n  error: string;\n}\n\n/**\n * Options that configure the validation rules used by a {@link PackageNameParser} instance.\n *\n * @remarks\n * The default validation is based on the npmjs.com registry's policy for published packages, and includes these\n * restrictions:\n *\n * - The package name cannot be longer than 214 characters.\n *\n * - The package name must not be empty.\n *\n * - Other than the `@` and `/` delimiters used for scopes, the only allowed characters\n *   are letters, numbers, `-`, `_`, and `.`.\n *\n * - The name must not start with a `.` or `_`.\n *\n * @public\n */\nexport interface IPackageNameParserOptions {\n  /**\n   * If true, allows upper-case letters in package names.\n   * This improves compatibility with some legacy private registries that still allow that.\n   */\n  allowUpperCase?: boolean;\n}\n\n/**\n * A configurable parser for validating and manipulating NPM package names such as `my-package` or `@scope/my-package`.\n *\n * @remarks\n * If you do not need to customize the parser configuration, it is recommended to use {@link PackageName}\n * which exposes these operations as a simple static class.\n *\n * @public\n */\nexport class PackageNameParser {\n  // encodeURIComponent() escapes all characters except:  A-Z a-z 0-9 - _ . ! ~ * ' ( )\n  // However, these are disallowed because they are shell characters:       ! ~ * ' ( )\n  private static readonly _invalidNameCharactersRegExp: RegExp = /[^A-Za-z0-9\\-_\\.]/;\n\n  private readonly _options: IPackageNameParserOptions;\n\n  public constructor(options: IPackageNameParserOptions = {}) {\n    this._options = { ...options };\n  }\n\n  /**\n   * This attempts to parse a package name that may include a scope component.\n   * The packageName must not be an empty string.\n   * @remarks\n   * This function will not throw an exception.\n   *\n   * @returns an {@link IParsedPackageNameOrError} structure whose `error` property will be\n   * nonempty if the string could not be parsed.\n   */\n  public tryParse(packageName: string): IParsedPackageNameOrError {\n    const result: IParsedPackageNameOrError = {\n      scope: '',\n      unscopedName: '',\n      error: ''\n    };\n\n    let input: string = packageName;\n\n    if (input === null || input === undefined) {\n      result.error = 'The package name must not be null or undefined';\n      return result;\n    }\n\n    // Rule from npmjs.com:\n    // \"The name must be less than or equal to 214 characters. This includes the scope for scoped packages.\"\n    if (packageName.length > 214) {\n      // Don't attempt to parse a ridiculously long input\n      result.error = 'The package name cannot be longer than 214 characters';\n      return result;\n    }\n\n    if (input[0] === '@') {\n      const indexOfScopeSlash: number = input.indexOf('/');\n      if (indexOfScopeSlash <= 0) {\n        result.scope = input;\n        result.error = `Error parsing \"${packageName}\": The scope must be followed by a slash`;\n        return result;\n      }\n\n      // Extract the scope substring\n      result.scope = input.substr(0, indexOfScopeSlash);\n\n      input = input.substr(indexOfScopeSlash + 1);\n    }\n\n    result.unscopedName = input;\n\n    if (result.scope === '@') {\n      result.error = `Error parsing \"${packageName}\": The scope name cannot be empty`;\n      return result;\n    }\n\n    if (result.unscopedName === '') {\n      result.error = 'The package name must not be empty';\n      return result;\n    }\n\n    // Rule from npmjs.com:\n    // \"The name can't start with a dot or an underscore.\"\n    if (result.unscopedName[0] === '.' || result.unscopedName[0] === '_') {\n      result.error = `The package name \"${packageName}\" starts with an invalid character`;\n      return result;\n    }\n\n    // Convert \"@scope/unscoped-name\" --> \"scopeunscoped-name\"\n    const nameWithoutScopeSymbols: string =\n      (result.scope ? result.scope.slice(1, -1) : '') + result.unscopedName;\n\n    if (!this._options.allowUpperCase) {\n      // \"New packages must not have uppercase letters in the name.\"\n      // This can't be enforced because \"old\" packages are still actively maintained.\n      // Example: https://www.npmjs.com/package/Base64\n      // However it's pretty reasonable to require the scope to be lower case\n      if (result.scope !== result.scope.toLowerCase()) {\n        result.error = `The package scope \"${result.scope}\" must not contain upper case characters`;\n        return result;\n      }\n    }\n\n    // \"The name ends up being part of a URL, an argument on the command line, and a folder name.\n    // Therefore, the name can't contain any non-URL-safe characters\"\n    const match: RegExpMatchArray | null = nameWithoutScopeSymbols.match(\n      PackageNameParser._invalidNameCharactersRegExp\n    );\n    if (match) {\n      result.error = `The package name \"${packageName}\" contains an invalid character: \"${match[0]}\"`;\n      return result;\n    }\n\n    return result;\n  }\n\n  /**\n   * Same as {@link PackageName.tryParse}, except this throws an exception if the input\n   * cannot be parsed.\n   * @remarks\n   * The packageName must not be an empty string.\n   */\n  public parse(packageName: string): IParsedPackageName {\n    const result: IParsedPackageNameOrError = this.tryParse(packageName);\n    if (result.error) {\n      throw new Error(result.error);\n    }\n    return result;\n  }\n\n  /**\n   * {@inheritDoc IParsedPackageName.scope}\n   */\n  public getScope(packageName: string): string {\n    return this.parse(packageName).scope;\n  }\n\n  /**\n   * {@inheritDoc IParsedPackageName.unscopedName}\n   */\n  public getUnscopedName(packageName: string): string {\n    return this.parse(packageName).unscopedName;\n  }\n\n  /**\n   * Returns true if the specified package name is valid, or false otherwise.\n   * @remarks\n   * This function will not throw an exception.\n   */\n  public isValidName(packageName: string): boolean {\n    const result: IParsedPackageNameOrError = this.tryParse(packageName);\n    return !result.error;\n  }\n\n  /**\n   * Throws an exception if the specified name is not a valid package name.\n   * The packageName must not be an empty string.\n   */\n  public validate(packageName: string): void {\n    this.parse(packageName);\n  }\n\n  /**\n   * Combines an optional package scope with an unscoped root name.\n   * @param scope - Must be either an empty string, or a scope name such as \"\\@example\"\n   * @param unscopedName - Must be a nonempty package name that does not contain a scope\n   * @returns A full package name such as \"\\@example/some-library\".\n   */\n  public combineParts(scope: string, unscopedName: string): string {\n    if (scope !== '') {\n      if (scope[0] !== '@') {\n        throw new Error('The scope must start with an \"@\" character');\n      }\n    }\n    if (scope.indexOf('/') >= 0) {\n      throw new Error('The scope must not contain a \"/\" character');\n    }\n\n    if (unscopedName[0] === '@') {\n      throw new Error('The unscopedName cannot start with an \"@\" character');\n    }\n    if (unscopedName.indexOf('/') >= 0) {\n      throw new Error('The unscopedName must not contain a \"/\" character');\n    }\n\n    let result: string;\n    if (scope === '') {\n      result = unscopedName;\n    } else {\n      result = scope + '/' + unscopedName;\n    }\n\n    // Make sure the result is a valid package name\n    this.validate(result);\n\n    return result;\n  }\n}\n\n/**\n * Provides basic operations for validating and manipulating NPM package names such as `my-package`\n * or `@scope/my-package`.\n *\n * @remarks\n * This is the default implementation of {@link PackageNameParser}, exposed as a convenient static class.\n * If you need to configure the parsing rules, use `PackageNameParser` instead.\n *\n * @public\n */\nexport class PackageName {\n  private static readonly _parser: PackageNameParser = new PackageNameParser();\n\n  /** {@inheritDoc PackageNameParser.tryParse} */\n  public static tryParse(packageName: string): IParsedPackageNameOrError {\n    return PackageName._parser.tryParse(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.parse} */\n  public static parse(packageName: string): IParsedPackageName {\n    return this._parser.parse(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.getScope} */\n  public static getScope(packageName: string): string {\n    return this._parser.getScope(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.getUnscopedName} */\n  public static getUnscopedName(packageName: string): string {\n    return this._parser.getUnscopedName(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.isValidName} */\n  public static isValidName(packageName: string): boolean {\n    return this._parser.isValidName(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.validate} */\n  public static validate(packageName: string): void {\n    return this._parser.validate(packageName);\n  }\n\n  /** {@inheritDoc PackageNameParser.combineParts} */\n  public static combineParts(scope: string, unscopedName: string): string {\n    return this._parser.combineParts(scope, unscopedName);\n  }\n}\n"]}